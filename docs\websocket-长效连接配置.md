# WebSocket长效连接和单一客户端配置指南

## 概述

本系统已实现WebSocket长效连接机制，确保只有在用户主动关闭页面时才会断开连接，并支持单一账号只能有一个活跃的WebSocket客户端。

## 主要功能

### 1. 长效连接机制

#### 前端配置优化
- **心跳间隔**: 10秒（更频繁的心跳检测）
- **连接超时**: 30秒
- **Ping超时**: 120秒（2分钟）
- **Ping间隔**: 30秒
- **最大重连次数**: 50次
- **保活机制**: 30秒间隔发送保活信号

#### 服务器配置优化
- **Ping超时**: 120秒（2分钟）
- **Ping间隔**: 30秒
- **连接超时**: 30秒
- **升级超时**: 30秒
- **缓冲区大小**: 100MB

### 2. 单一客户端控制

#### 实现机制
- 每个客户端生成唯一的`clientId`
- 服务器检测到同一用户的重复连接时，自动断开旧连接
- 新连接建立后，旧连接收到`force_disconnect`事件

#### 客户端标识生成
```javascript
// 格式: userId_timestamp_random
const clientId = `${userId}_${timestamp}_${random}`
```

### 3. 心跳和保活机制

#### 心跳机制
- **频率**: 每10秒发送一次心跳
- **超时检测**: 5秒内未收到响应视为连接异常
- **自动重连**: 检测到连接断开时自动重连

#### 保活机制
- **频率**: 每30秒发送一次保活信号
- **内容**: 包含客户端ID、时间戳、页面可见性状态
- **用途**: 确保长时间无操作时连接不被断开

### 4. 页面生命周期处理

#### 页面可见性变化
- 页面隐藏时：保持连接，减少日志输出
- 页面重新可见时：重新生成客户端ID，检查连接状态

#### 页面关闭处理
- 发送`client_closing`事件通知服务器
- 标记页面正在关闭，避免不必要的重连

## 配置参数

### 前端配置 (websocketManager.js)
```javascript
{
  heartbeatInterval: 10000,        // 心跳间隔10秒
  heartbeatTimeout: 5000,          // 心跳超时5秒
  keepAliveInterval: 30000,        // 保活间隔30秒
  maxReconnectAttempts: 50,        // 最大重连次数
  connectionCheckInterval: 15000,   // 连接检查间隔15秒
  forceReconnectInterval: 45000,   // 强制重连检查45秒
  
  // Socket.IO配置
  socketOptions: {
    transports: ['websocket'],
    timeout: 30000,
    pingTimeout: 120000,
    pingInterval: 30000,
    maxHttpBufferSize: 1e8
  }
}
```

### 服务器配置 (server-variables.js)
```javascript
const WEBSOCKET_CONFIG = {
  PING_TIMEOUT: 120000,      // 120秒
  PING_INTERVAL: 30000,      // 30秒
  MAX_HTTP_BUFFER_SIZE: 1e8, // 100MB
  UPGRADE_TIMEOUT: 30000,    // 30秒升级超时
  CONNECT_TIMEOUT: 30000,    // 30秒连接超时
  HEARTBEAT_TIMEOUT: 60000,  // 60秒心跳超时
  HEARTBEAT_INTERVAL: 25000  // 25秒心跳间隔
}
```

## 事件处理

### 客户端事件
- `connection_confirmed`: 连接确认，包含会话ID
- `force_disconnect`: 强制断开通知（重复登录）
- `keep_alive`: 保活信号
- `client_closing`: 页面关闭通知
- `client_disconnect`: 客户端断开通知

### 服务器事件
- `web_client_connect`: Web客户端连接注册
- `keep_alive`: 处理保活信号
- `client_closing`: 处理页面关闭通知
- `client_disconnect`: 处理客户端断开通知

## 使用方法

### 1. 基本连接
```javascript
import { initWebSocket } from '@/utils/websocketManager'

// 初始化WebSocket连接
await initWebSocket()
```

### 2. 监听连接事件
```javascript
import { getWebSocketManager } from '@/utils/websocketManager'

const wsManager = getWebSocketManager()

// 监听重复登录事件
wsManager.on('duplicate_login', (data) => {
  console.log('检测到重复登录:', data)
  // 显示用户提示
})

// 监听连接确认
wsManager.on('connection_confirmed', (data) => {
  console.log('连接已确认:', data)
})
```

### 3. 手动发送保活信号
```javascript
const wsManager = getWebSocketManager()
wsManager.send('keep_alive', {
  clientId: wsManager.clientId,
  timestamp: Date.now(),
  pageVisible: !document.hidden
})
```

## 测试页面

访问 `/websocket-test.html` 可以测试长效连接功能：

- 连接状态监控
- 心跳和保活测试
- 重复登录测试
- 连接时长统计
- 实时日志查看

## 故障排除

### 连接频繁断开
1. 检查网络稳定性
2. 确认服务器配置是否正确
3. 查看浏览器控制台错误信息
4. 检查防火墙和代理设置

### 重复登录检测不工作
1. 确认用户ID正确传递
2. 检查服务器端单一客户端逻辑
3. 验证客户端ID生成是否唯一

### 心跳机制异常
1. 检查心跳间隔配置
2. 确认服务器端心跳处理
3. 查看网络延迟情况

## 性能优化建议

1. **合理设置心跳间隔**: 平衡连接稳定性和服务器负载
2. **使用节流日志**: 避免频繁的日志输出影响性能
3. **页面不可见时减少活动**: 降低后台资源消耗
4. **及时清理断开的连接**: 释放服务器资源

## 注意事项

1. 长效连接会占用服务器资源，需要合理配置连接数限制
2. 在移动设备上，系统可能会在后台杀死连接
3. 某些网络环境（如公司防火墙）可能会强制断开长时间空闲的连接
4. 建议配合服务器端的连接池管理和负载均衡
