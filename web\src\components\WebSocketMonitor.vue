<template>
  <div class="websocket-monitor">
    <!-- 连接状态指示器 -->
    <div class="connection-indicator" :class="connectionStatusClass">
      <i :class="connectionIcon"></i>
      <span>{{ connectionStatusText }}</span>
      <el-badge v-if="reconnectAttempts > 0" :value="reconnectAttempts" class="reconnect-badge">
        <span>重连</span>
      </el-badge>
    </div>

    <!-- 连接统计信息 -->
    <div class="connection-stats" v-if="showStats">
      <div class="stat-item">
        <span class="stat-label">连接时长:</span>
        <span class="stat-value">{{ formatDuration(connectionDuration) }}</span>
      </div>
      <div class="stat-item" v-if="networkLatency !== null">
        <span class="stat-label">网络延迟:</span>
        <span class="stat-value">{{ networkLatency }}ms</span>
      </div>
      <div class="stat-item">
        <span class="stat-label">客户端ID:</span>
        <span class="stat-value">{{ clientId || '-' }}</span>
      </div>
    </div>

    <!-- 连接通知 -->
    <div v-if="notification" class="connection-notification" :class="notification.type">
      <i :class="getNotificationIcon(notification.type)"></i>
      <div class="notification-content">
        <div class="notification-title">{{ notification.title }}</div>
        <div class="notification-message">{{ notification.message }}</div>
      </div>
      <el-button 
        type="text" 
        icon="el-icon-close" 
        @click="dismissNotification"
        class="notification-close"
      />
    </div>

    <!-- 详细信息面板 -->
    <el-collapse v-if="showDetails" v-model="activeCollapse">
      <el-collapse-item title="连接详情" name="details">
        <div class="connection-details">
          <div class="detail-row">
            <span class="detail-label">会话ID:</span>
            <span class="detail-value">{{ sessionId || '-' }}</span>
          </div>
          <div class="detail-row">
            <span class="detail-label">重连次数:</span>
            <span class="detail-value">{{ reconnectAttempts }}</span>
          </div>
          <div class="detail-row">
            <span class="detail-label">最后心跳:</span>
            <span class="detail-value">{{ formatTime(lastHeartbeatTime) }}</span>
          </div>
          <div class="detail-row">
            <span class="detail-label">页面可见:</span>
            <span class="detail-value">{{ pageVisible ? '是' : '否' }}</span>
          </div>
        </div>
      </el-collapse-item>
      
      <el-collapse-item title="连接事件" name="events">
        <div class="connection-events">
          <div 
            v-for="event in recentEvents" 
            :key="event.timestamp"
            class="event-item"
            :class="event.type"
          >
            <div class="event-time">{{ formatTime(event.timestamp) }}</div>
            <div class="event-type">{{ getEventTypeText(event.type) }}</div>
            <div class="event-data" v-if="event.data">{{ JSON.stringify(event.data) }}</div>
          </div>
        </div>
      </el-collapse-item>
    </el-collapse>
  </div>
</template>

<script>
import { getWebSocketManager } from '@/utils/websocketManager'

export default {
  name: 'WebSocketMonitor',
  props: {
    showStats: {
      type: Boolean,
      default: true
    },
    showDetails: {
      type: Boolean,
      default: false
    },
    autoHideNotification: {
      type: Boolean,
      default: true
    },
    notificationDuration: {
      type: Number,
      default: 5000
    }
  },
  data() {
    return {
      wsManager: null,
      connectionStats: {},
      notification: null,
      notificationTimer: null,
      activeCollapse: ['details'],
      updateTimer: null
    }
  },
  computed: {
    connectionStatusClass() {
      if (this.connectionStats.isConnected) {
        return 'connected'
      } else if (this.reconnectAttempts > 0) {
        return 'reconnecting'
      } else {
        return 'disconnected'
      }
    },
    connectionIcon() {
      if (this.connectionStats.isConnected) {
        return 'el-icon-success'
      } else if (this.reconnectAttempts > 0) {
        return 'el-icon-loading'
      } else {
        return 'el-icon-error'
      }
    },
    connectionStatusText() {
      if (this.connectionStats.isConnected) {
        return '已连接'
      } else if (this.reconnectAttempts > 0) {
        return '重连中...'
      } else {
        return '未连接'
      }
    },
    connectionDuration() {
      return this.connectionStats.connectionDuration || 0
    },
    networkLatency() {
      return this.connectionStats.networkLatency
    },
    clientId() {
      return this.connectionStats.clientId
    },
    sessionId() {
      return this.connectionStats.sessionId
    },
    reconnectAttempts() {
      return this.connectionStats.reconnectAttempts || 0
    },
    lastHeartbeatTime() {
      return this.connectionStats.lastHeartbeatTime
    },
    pageVisible() {
      return this.connectionStats.pageVisible
    },
    recentEvents() {
      return this.connectionStats.connectionEvents || []
    }
  },
  mounted() {
    this.initWebSocketMonitor()
  },
  beforeDestroy() {
    this.cleanup()
  },
  methods: {
    initWebSocketMonitor() {
      this.wsManager = getWebSocketManager()
      
      // 监听连接通知
      this.wsManager.on('connection_notification', this.handleConnectionNotification)
      this.wsManager.on('duplicate_login', this.handleDuplicateLogin)
      this.wsManager.on('connection_established', this.handleConnectionEstablished)
      this.wsManager.on('connection_lost', this.handleConnectionLost)
      
      // 定期更新连接统计
      this.updateConnectionStats()
      this.updateTimer = setInterval(() => {
        this.updateConnectionStats()
      }, 1000)
    },
    
    updateConnectionStats() {
      if (this.wsManager) {
        this.connectionStats = this.wsManager.getConnectionStats()
      }
    },
    
    handleConnectionNotification(data) {
      this.showNotification(data.type, data.title, data.message)
    },
    
    handleDuplicateLogin(data) {
      this.showNotification('warning', '重复登录', data.message || '检测到账号在其他地方登录')
    },
    
    handleConnectionEstablished() {
      this.showNotification('success', '连接成功', '与服务器的连接已建立')
    },
    
    handleConnectionLost(data) {
      this.showNotification('error', '连接断开', `连接已断开: ${data.reason}`)
    },
    
    showNotification(type, title, message) {
      this.notification = {
        type,
        title,
        message,
        timestamp: Date.now()
      }
      
      // 自动隐藏通知
      if (this.autoHideNotification) {
        this.clearNotificationTimer()
        this.notificationTimer = setTimeout(() => {
          this.dismissNotification()
        }, this.notificationDuration)
      }
    },
    
    dismissNotification() {
      this.notification = null
      this.clearNotificationTimer()
    },
    
    clearNotificationTimer() {
      if (this.notificationTimer) {
        clearTimeout(this.notificationTimer)
        this.notificationTimer = null
      }
    },
    
    getNotificationIcon(type) {
      const icons = {
        success: 'el-icon-success',
        warning: 'el-icon-warning',
        error: 'el-icon-error',
        info: 'el-icon-info'
      }
      return icons[type] || 'el-icon-info'
    },
    
    getEventTypeText(type) {
      const texts = {
        connection_established: '连接建立',
        connection_lost: '连接断开',
        heartbeat_timeout: '心跳超时',
        heartbeat_response: '心跳响应',
        force_disconnect: '强制断开',
        send_error: '发送错误'
      }
      return texts[type] || type
    },
    
    formatDuration(ms) {
      if (!ms) return '-'
      const seconds = Math.floor(ms / 1000)
      const minutes = Math.floor(seconds / 60)
      const hours = Math.floor(minutes / 60)
      
      if (hours > 0) {
        return `${hours}时${minutes % 60}分${seconds % 60}秒`
      } else if (minutes > 0) {
        return `${minutes}分${seconds % 60}秒`
      } else {
        return `${seconds}秒`
      }
    },
    
    formatTime(timestamp) {
      if (!timestamp) return '-'
      return new Date(timestamp).toLocaleTimeString()
    },
    
    cleanup() {
      if (this.updateTimer) {
        clearInterval(this.updateTimer)
        this.updateTimer = null
      }
      this.clearNotificationTimer()
      
      if (this.wsManager) {
        this.wsManager.off('connection_notification', this.handleConnectionNotification)
        this.wsManager.off('duplicate_login', this.handleDuplicateLogin)
        this.wsManager.off('connection_established', this.handleConnectionEstablished)
        this.wsManager.off('connection_lost', this.handleConnectionLost)
      }
    }
  }
}
</script>

<style scoped>
.websocket-monitor {
  font-size: 14px;
}

.connection-indicator {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  border-radius: 4px;
  margin-bottom: 10px;
  font-weight: 500;
}

.connection-indicator.connected {
  background-color: #f0f9ff;
  color: #059669;
  border: 1px solid #a7f3d0;
}

.connection-indicator.reconnecting {
  background-color: #fffbeb;
  color: #d97706;
  border: 1px solid #fde68a;
}

.connection-indicator.disconnected {
  background-color: #fef2f2;
  color: #dc2626;
  border: 1px solid #fecaca;
}

.connection-indicator i {
  margin-right: 8px;
}

.reconnect-badge {
  margin-left: 8px;
}

.connection-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 10px;
  margin-bottom: 15px;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  padding: 6px 10px;
  background-color: #f8fafc;
  border-radius: 4px;
  border: 1px solid #e2e8f0;
}

.stat-label {
  color: #64748b;
  font-size: 12px;
}

.stat-value {
  font-weight: 500;
  color: #1e293b;
  font-size: 12px;
}

.connection-notification {
  display: flex;
  align-items: flex-start;
  padding: 12px;
  border-radius: 6px;
  margin-bottom: 15px;
  border-left: 4px solid;
}

.connection-notification.success {
  background-color: #f0fdf4;
  border-left-color: #22c55e;
  color: #15803d;
}

.connection-notification.warning {
  background-color: #fffbeb;
  border-left-color: #f59e0b;
  color: #d97706;
}

.connection-notification.error {
  background-color: #fef2f2;
  border-left-color: #ef4444;
  color: #dc2626;
}

.connection-notification.info {
  background-color: #f0f9ff;
  border-left-color: #3b82f6;
  color: #1d4ed8;
}

.connection-notification i {
  margin-right: 10px;
  margin-top: 2px;
}

.notification-content {
  flex: 1;
}

.notification-title {
  font-weight: 600;
  margin-bottom: 4px;
}

.notification-message {
  font-size: 13px;
  opacity: 0.9;
}

.notification-close {
  margin-left: 10px;
  padding: 0;
  min-width: auto;
}

.connection-details {
  display: grid;
  gap: 8px;
}

.detail-row {
  display: flex;
  justify-content: space-between;
  padding: 6px 0;
  border-bottom: 1px solid #f1f5f9;
}

.detail-label {
  color: #64748b;
  font-size: 13px;
}

.detail-value {
  font-weight: 500;
  color: #1e293b;
  font-size: 13px;
}

.connection-events {
  max-height: 200px;
  overflow-y: auto;
}

.event-item {
  display: grid;
  grid-template-columns: auto 1fr auto;
  gap: 10px;
  padding: 8px;
  border-bottom: 1px solid #f1f5f9;
  font-size: 12px;
}

.event-time {
  color: #64748b;
  white-space: nowrap;
}

.event-type {
  font-weight: 500;
}

.event-data {
  color: #64748b;
  font-family: monospace;
  font-size: 11px;
  word-break: break-all;
}

.event-item.connection_established .event-type {
  color: #059669;
}

.event-item.connection_lost .event-type {
  color: #dc2626;
}

.event-item.heartbeat_timeout .event-type {
  color: #d97706;
}
</style>
