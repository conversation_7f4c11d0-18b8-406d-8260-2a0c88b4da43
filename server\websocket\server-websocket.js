/**
 * 服务器WebSocket和静态文件模块 - 第7个文件 (原始文件第11401-13281行)
 * 包含WebSocket处理、静态文件服务、健康检查等功能
 */

const path = require('path');
const express = require('express');

// WebSocket和静态文件模块设置函数
async function setupServerWebSocket(app, server, io, coreData, authData) {
  console.log('🔧 设置WebSocket和静态文件模块...');

  const {
    devices,
    webClients,
    recentlyDisconnectedDevices,
    throttledLog,
    pool,
    pendingCommands,
    xiaohongshuLogService,
    xianyuLogService,
    cleanupXiaohongshuTasksForDevice,
    cleanupXianyuTasksForDevice
  } = coreData;

  // 静态文件服务
  app.use(express.static(path.join(__dirname, '../public')));
  app.use('/uploads', express.static(path.join(__dirname, 'uploads')));

  // 健康检查端点已移动到server-management.js模块中，避免重复定义

  // 根路径重定向已移除 - 由主页路由模块处理
  // app.get('/', (req, res) => {
  //   res.redirect('/device-test.html');
  // });

  // 清理函数现在从coreData中获取，避免重复定义

  // WebSocket连接处理
  io.on('connection', (socket) => {
    console.log('新的WebSocket连接:', socket.id);

    // 设备注册
    socket.on('register_device', (data) => {
      const { deviceId, deviceName, deviceInfo } = data;
      
      if (!deviceId || !deviceName) {
        socket.emit('registration_error', { message: '设备ID和名称不能为空' });
        return;
      }

      // 检查设备是否在最近断开列表中（防止立即重连）
      if (recentlyDisconnectedDevices.has(deviceId)) {
        const disconnectTime = recentlyDisconnectedDevices.get(deviceId);
        const timeSinceDisconnect = Date.now() - disconnectTime;
        const cooldownPeriod = 10000; // 10秒冷却期

        if (timeSinceDisconnect < cooldownPeriod) {
          const remainingTime = Math.ceil((cooldownPeriod - timeSinceDisconnect) / 1000);
          console.log(`设备 ${deviceId} 在冷却期内尝试重连，剩余 ${remainingTime} 秒`);
          socket.emit('registration_error', {
            message: `设备刚刚断开连接，请等待 ${remainingTime} 秒后再重新连接`,
            cooldownRemaining: remainingTime
          });
          return;
        } else {
          // 冷却期已过，移除记录
          recentlyDisconnectedDevices.delete(deviceId);
          console.log(`设备 ${deviceId} 冷却期已过，允许重新连接`);
        }
      }

      // 检查是否有相同IP的设备已连接
      const currentIP = deviceInfo?.ipAddress || socket.handshake.address;
      if (currentIP) {
        for (const [socketId, device] of devices) {
          if (device.deviceInfo && device.deviceInfo.ipAddress === currentIP &&
              device.status === 'online' && socketId !== socket.id) {
            console.log(`检测到同IP设备重连: ${currentIP}, 断开旧连接: ${device.deviceName} (${device.deviceId})`);
            
            // 通知Web客户端旧设备离线
            for (const [clientSocketId] of webClients) {
              const clientSocket = io.sockets.sockets.get(clientSocketId);
              if (clientSocket) {
                clientSocket.emit('device_offline', {
                  deviceId: device.deviceId
                });
              }
            }

            // 断开旧连接
            const oldSocket = io.sockets.sockets.get(socketId);
            if (oldSocket) {
              oldSocket.disconnect(true);
            }

            // 移除旧设备连接
            devices.delete(socketId);
            console.log(`已移除旧设备连接: ${socketId}`);
            break;
          }
        }
      }

      // 注册新设备
      const deviceData = {
        socketId: socket.id,
        deviceId,
        deviceName,
        deviceInfo: {
          ...deviceInfo,
          ipAddress: currentIP
        },
        status: 'online',
        connectedAt: new Date(),
        lastSeen: new Date()
      };

      devices.set(socket.id, deviceData);
      socket.join(deviceId); // 加入设备房间，用于定向发送消息

      console.log(`WebSocket设备已注册: ${deviceName} (${deviceId})`);

      // 通知设备注册成功
      socket.emit('registration_success', {
        message: '设备注册成功',
        deviceId: deviceId
      });

      // 通知所有Web客户端有新设备连接
      const statusChange = {
        type: 'device_connected',
        deviceId: deviceId,
        deviceName: deviceName,
        deviceInfo: deviceInfo,
        timestamp: new Date()
      };

      for (const [clientSocketId] of webClients) {
        const clientSocket = io.sockets.sockets.get(clientSocketId);
        if (clientSocket) {
          clientSocket.emit('device_status_changed', statusChange);
        }
      }
    });

    // Web客户端注册 - 兼容两种事件名称
    socket.on('register_web_client', () => {
      webClients.set(socket.id, {
        socketId: socket.id,
        connectedAt: new Date(),
        lastSeen: new Date()
      });

      console.log(`Web客户端已注册: ${socket.id}`);

      // 发送当前设备列表给新连接的Web客户端
      const deviceList = Array.from(devices.values()).map(device => ({
        deviceId: device.deviceId,
        deviceName: device.deviceName,
        deviceInfo: device.deviceInfo,
        status: device.status,
        connectedAt: device.connectedAt,
        lastSeen: device.lastSeen
      }));

      socket.emit('device_list_update', deviceList);
    });

    // Web客户端连接 - 前端使用的事件名称
    socket.on('web_client_connect', (data) => {
      const userId = data?.userId || 'anonymous';
      const clientId = data?.clientId;

      // 单一客户端控制：检查是否已有相同用户的连接
      if (userId !== 'anonymous') {
        // 查找并断开同一用户的其他连接
        for (const [existingSocketId, clientData] of webClients) {
          if (clientData.userId === userId && existingSocketId !== socket.id) {
            console.log(`🔄 检测到用户 ${userId} 的重复连接，断开旧连接: ${existingSocketId}`);
            const existingSocket = io.sockets.sockets.get(existingSocketId);
            if (existingSocket) {
              existingSocket.emit('force_disconnect', {
                reason: 'duplicate_login',
                message: '检测到新的登录，当前连接将被断开'
              });
              existingSocket.disconnect(true);
            }
            webClients.delete(existingSocketId);
          }
        }
      }

      // 注册新的Web客户端
      webClients.set(socket.id, {
        socketId: socket.id,
        userId: userId,
        clientId: clientId,
        clientType: data?.clientType || 'web',
        sessionId: data?.sessionId,
        userAgent: data?.userAgent,
        url: data?.url,
        connectedAt: new Date(),
        lastSeen: new Date()
      });

      console.log(`Web客户端已连接: ${userId} (ClientID: ${clientId})`);

      // 发送连接确认
      socket.emit('connection_confirmed', {
        message: '服务器确认连接成功',
        clientId: clientId,
        sessionId: data?.sessionId,
        clientType: data?.clientType || 'web',
        timestamp: new Date().toISOString()
      });

      // 发送当前设备列表给新连接的Web客户端
      const deviceList = Array.from(devices.values()).map(device => ({
        deviceId: device.deviceId,
        deviceName: device.deviceName,
        deviceInfo: device.deviceInfo,
        status: device.status,
        connectedAt: device.connectedAt,
        lastSeen: device.lastSeen
      }));

      socket.emit('device_list_update', deviceList);
    });

    // 处理设备状态同步请求
    socket.on('request_device_status_sync', async () => {
      console.log('🔄 收到设备状态同步请求，发送当前设备状态...');

      // 发送当前所有设备的状态
      const deviceList = [];
      for (const [socketId, device] of devices) {
        deviceList.push({
          deviceId: device.deviceId,
          deviceName: device.deviceName,
          status: device.status,
          lastSeen: device.lastSeen || device.connectedAt,
          ipAddress: device.deviceInfo?.ipAddress
        });
      }

      // 发送设备列表更新
      socket.emit('device_list_update', deviceList);

      // 为每个设备发送状态更新事件
      deviceList.forEach(device => {
        socket.emit('device_status_update', {
          deviceId: device.deviceId,
          status: device.status,
          lastSeen: device.lastSeen
        });
      });

      console.log(`📡 已同步 ${deviceList.length} 个设备的状态`);
    });

    // 处理保活信号
    socket.on('keep_alive', (data) => {
      const webClient = webClients.get(socket.id);
      if (webClient) {
        webClient.lastSeen = new Date();
        // 使用节流日志，避免频繁输出保活信息
        throttledLog(`keep_alive_${webClient.userId}`, `Web客户端保活: ${webClient.userId} (ClientID: ${data?.clientId})`);
      }
    });

    // 处理客户端关闭通知
    socket.on('client_closing', (data) => {
      console.log(`📱 客户端关闭通知: ${data?.clientId}, 原因: ${data?.reason}`);
      const webClient = webClients.get(socket.id);
      if (webClient) {
        webClient.isClosing = true;
        webClient.closeReason = data?.reason;
      }
    });

    // 处理客户端断开通知
    socket.on('client_disconnect', (data) => {
      console.log(`📱 客户端断开通知: ${data?.clientId}, 原因: ${data?.reason}`);
    });

    // 处理设备断开连接
    socket.on('disconnect', async () => {
      console.log('WebSocket连接断开:', socket.id);

      // 检查是否是设备连接
      const device = devices.get(socket.id);
      if (device) {
        console.log(`设备断开连接: ${device.deviceName} (${device.deviceId})`);

        // 在设备断开前，先清理该设备的所有执行状态
        console.log(`设备 ${device.deviceId} 断开连接，开始清理相关状态`);

        // 1. 清理小红书任务状态
        await cleanupXiaohongshuTasksForDevice(device.deviceId);

        // 2. 清理闲鱼任务状态
        await cleanupXianyuTasksForDevice(device.deviceId);

        // 3. 发送停止脚本命令（如果是HTTP设备）
        console.log(`向设备 ${device.deviceId} 发送停止脚本命令（设备断开）`);
        try {
          // 检查是否是HTTP设备（socketId以http_开头或者在pendingCommands中有队列）
          const isHttpDevice = socket.id.startsWith('http_') || (pendingCommands && pendingCommands.has(device.deviceId));

          if (isHttpDevice) {
            console.log(`设备 ${device.deviceId} 是HTTP连接设备，添加停止命令到队列`);

            // 对于HTTP设备，添加停止命令到pendingCommands队列
            if (pendingCommands) {
              // 如果设备队列不存在，创建一个新的队列
              if (!pendingCommands.has(device.deviceId)) {
                pendingCommands.set(device.deviceId, []);
                console.log(`为HTTP设备创建新的命令队列: ${device.deviceId}`);
              }

              pendingCommands.get(device.deviceId).push({
                type: 'stop_script',
                script: 'STOP_SCRIPT_COMMAND',
                deviceId: device.deviceId,
                reason: 'device_disconnected',
                message: '设备断开连接，停止当前任务脚本（保持控制器运行）',
                stopTaskOnly: true, // 明确标识只停止任务脚本
                timestamp: new Date().toISOString()
              });
              console.log(`已添加停止命令到HTTP设备队列: ${device.deviceId}`);
            } else {
              console.error('pendingCommands 未初始化，无法添加停止命令');
            }
          } else {
            // WebSocket设备：尝试发送停止命令（虽然设备可能已经断开）
            console.log(`设备 ${device.deviceId} 是WebSocket连接设备，尝试发送停止命令`);

            // 发送停止脚本命令
            socket.emit('stop_script', {
              reason: 'device_disconnected',
              message: '设备断开连接，停止当前任务脚本（保持控制器运行）',
              stopTaskOnly: true, // 明确标识只停止任务脚本
              timestamp: new Date().toISOString()
            });

            // 发送通用脚本命令停止
            socket.emit('script_command', {
              type: 'stop_script',
              deviceId: device.deviceId,
              reason: 'device_disconnected',
              message: '设备断开连接，停止当前任务脚本（保持控制器运行）',
              stopTaskOnly: true, // 明确标识只停止任务脚本
              timestamp: new Date().toISOString()
            });
          }

          console.log(`已向设备 ${device.deviceId} 发送停止脚本命令`);
        } catch (error) {
          console.error(`向设备 ${device.deviceId} 发送停止脚本命令失败:`, error);
        }

        // 更新数据库中正在执行的任务状态为失败
        if (coreData.pool) {
          try {
            // 更新小红书执行日志
            await coreData.pool.execute(`
              UPDATE xiaohongshu_execution_logs
              SET status = 'failed',
                  error_message = '设备断开连接',
                  end_time = NOW(),
                  progress = 0
              WHERE device_id = ? AND status IN ('running', 'pending')
            `, [device.deviceId]);

            // 更新闲鱼执行日志
            await coreData.pool.execute(`
              UPDATE xianyu_execution_logs
              SET status = 'failed',
                  error_message = '设备断开连接',
                  end_time = NOW(),
                  progress = 0
              WHERE device_id = ? AND status IN ('running', 'pending')
            `, [device.deviceId]);

            console.log(`已更新设备 ${device.deviceId} 的执行日志状态为失败`);
          } catch (dbError) {
            console.error(`更新设备 ${device.deviceId} 执行日志失败:`, dbError);
          }
        }

        // 添加到最近断开列表
        recentlyDisconnectedDevices.set(device.deviceId, Date.now());

        // 通知所有Web客户端设备离线
        const statusChange = {
          type: 'device_disconnected',
          deviceId: device.deviceId,
          deviceName: device.deviceName,
          timestamp: new Date()
        };

        for (const [clientSocketId] of webClients) {
          const clientSocket = io.sockets.sockets.get(clientSocketId);
          if (clientSocket) {
            clientSocket.emit('device_status_changed', statusChange);
            clientSocket.emit('device_offline', {
              deviceId: device.deviceId
            });

            // 发送设备状态更新事件，确保前端状态同步
            clientSocket.emit('device_status_update', {
              deviceId: device.deviceId,
              status: 'offline',
              lastSeen: new Date()
            });

            // 通知前端清除该设备的脚本执行状态
            clientSocket.emit('clear_device_script_status', {
              deviceId: device.deviceId,
              reason: 'device_disconnected',
              message: '设备已断开连接，清除脚本状态',
              timestamp: new Date().toISOString()
            });

            // 通知前端清除小红书和闲鱼的执行状态
            clientSocket.emit('xiaohongshu_execution_completed', {
              deviceId: device.deviceId,
              status: 'cancelled',
              message: '设备断开连接，任务已取消',
              timestamp: new Date().toISOString()
            });

            clientSocket.emit('xianyu_execution_completed', {
              deviceId: device.deviceId,
              status: 'cancelled',
              message: '设备断开连接，任务已取消',
              timestamp: new Date().toISOString()
            });
          }
        }

        // 移除设备连接
        devices.delete(socket.id);
      }

      // 检查是否是Web客户端连接
      const webClient = webClients.get(socket.id);
      if (webClient) {
        console.log(`Web客户端断开连接: ${socket.id}`);
        webClients.delete(socket.id);
      }
    });

    // 处理脚本执行结果
    socket.on('script_result', (data) => {
      const { logId, result, status, deviceId } = data;
      console.log(`收到脚本执行结果: ${deviceId}, 状态: ${status}`);

      // 恢复设备状态为在线
      const device = devices.get(socket.id);
      if (device) {
        device.status = 'online';
        device.lastActivity = new Date();
        console.log(`脚本执行完成，设备状态已恢复为在线: ${device.deviceId}`);

        // 广播设备状态变化
        for (const [clientSocketId] of webClients) {
          const clientSocket = io.sockets.sockets.get(clientSocketId);
          if (clientSocket) {
            clientSocket.emit('device_status_changed', {
              type: 'device_status_updated',
              deviceId: device.deviceId,
              status: 'online',
              message: '脚本执行完成，设备恢复在线',
              timestamp: new Date().toISOString()
            });
          }
        }
      }

      // 通知Web客户端脚本执行完成
      const completionEvent = {
        deviceId: deviceId,
        logId: logId,
        status: status,
        result: result,
        timestamp: new Date().toISOString()
      };

      for (const [clientSocketId] of webClients) {
        const clientSocket = io.sockets.sockets.get(clientSocketId);
        if (clientSocket) {
          clientSocket.emit('script_execution_completed', completionEvent);
        }
      }
    });

    // 处理脚本停止确认
    socket.on('script_stopped', (data) => {
      const device = devices.get(socket.id);
      if (device) {
        console.log(`收到脚本停止确认: ${device.deviceName} (${device.deviceId})`);

        // 恢复设备状态为在线
        device.status = 'online';
        device.lastActivity = new Date();

        // 广播设备状态变化
        for (const [clientSocketId] of webClients) {
          const clientSocket = io.sockets.sockets.get(clientSocketId);
          if (clientSocket) {
            clientSocket.emit('device_status_changed', {
              type: 'device_status_updated',
              deviceId: device.deviceId,
              status: 'online',
              message: '脚本已停止，设备恢复在线',
              timestamp: new Date().toISOString()
            });
          }
        }

        console.log(`设备状态已恢复为在线: ${device.deviceId}`);
      }
    });

    // 监听小红书停止脚本事件
    socket.on('xiaohongshu_stop_script', async (data) => {
      console.log('🛑 收到小红书停止脚本事件:', data);
      const { functionType, deviceId, logId, taskId } = data;

      try {
        // 查找目标设备
        let targetDevice = null;
        let targetSocket = null;

        for (const [socketId, deviceData] of devices) {
          if (deviceData.deviceId === deviceId) {
            targetDevice = deviceData;
            targetSocket = io.sockets.sockets.get(socketId);
            break;
          }
        }

        if (targetDevice && targetSocket) {
          console.log(`找到目标设备: ${deviceId}, 发送停止命令`);

          // 发送停止命令到设备
          targetSocket.emit('stop_script', {
            functionType: functionType,
            taskId: taskId || logId,
            reason: '用户手动停止',
            timestamp: new Date().toISOString()
          });

          // 更新设备状态为停止中
          targetDevice.status = 'stopping';
          targetDevice.lastActivity = new Date();

          console.log(`已发送停止命令到设备: ${deviceId}`);
        } else {
          console.log(`未找到设备: ${deviceId}, 可能是HTTP设备`);

          // 对于HTTP设备，通过pendingCommands发送停止命令
          const { pendingCommands } = coreData;
          if (pendingCommands) {
            // 如果设备队列不存在，创建一个新的队列
            if (!pendingCommands.has(deviceId)) {
              pendingCommands.set(deviceId, []);
              console.log(`为HTTP设备创建新的命令队列: ${deviceId}`);
            }

            pendingCommands.get(deviceId).push({
              type: 'stop_script',
              functionType: functionType,
              taskId: taskId || logId,
              reason: '用户手动停止',
              timestamp: new Date().toISOString()
            });
            console.log(`已添加停止命令到HTTP设备队列: ${deviceId}`);

            // 🔥 关键修复：为HTTP设备添加延迟状态恢复逻辑
            setTimeout(async () => {
              console.log(`开始恢复HTTP设备状态: ${deviceId}`);

              try {
                // 直接更新内存中的设备状态
                let deviceToUpdate = null;
                for (const [socketId, deviceData] of devices) {
                  if (deviceData.deviceId === deviceId) {
                    deviceToUpdate = deviceData;
                    break;
                  }
                }

                if (deviceToUpdate) {
                  deviceToUpdate.status = 'online';
                  deviceToUpdate.lastActivity = new Date();
                  console.log(`HTTP设备内存状态已恢复为在线: ${deviceId}`);
                } else {
                  console.log(`HTTP设备 ${deviceId} 在内存中未找到，可能已断开连接`);
                }

                // 更新数据库状态
                if (pool) {
                  await pool.execute(`
                    UPDATE devices SET status = 'online', last_seen = NOW()
                    WHERE device_id = ?
                  `, [deviceId]);
                  console.log(`HTTP设备数据库状态已更新为在线: ${deviceId}`);
                }

                // 广播设备状态变化
                io.emit('device_status_update', {
                  deviceId: deviceId,
                  status: 'online',
                  lastSeen: new Date().toISOString(),
                  message: '脚本已停止，设备恢复在线'
                });
                console.log(`已广播HTTP设备状态更新: ${deviceId} -> online`);

              } catch (error) {
                console.error(`恢复HTTP设备状态失败: ${deviceId}`, error);
              }
            }, 5000); // HTTP设备延迟5秒恢复状态

          } else {
            console.error('pendingCommands 未初始化，无法添加停止命令');
          }
        }

        // 通知Web客户端脚本停止中
        io.emit('xiaohongshu_script_stopping', {
          functionType,
          deviceId,
          taskId: taskId || logId,
          status: 'stopping',
          message: '正在停止脚本执行'
        });

      } catch (error) {
        console.error('处理停止脚本事件失败:', error);
      }
    });

    // 处理闲鱼脚本停止确认
    socket.on('xianyu_script_stopped', (data) => {
      const device = devices.get(socket.id);
      if (device) {
        console.log(`收到闲鱼脚本停止确认: ${device.deviceName} (${device.deviceId})`);

        // 恢复设备状态为在线
        device.status = 'online';
        device.lastActivity = new Date();

        // 广播设备状态变化
        for (const [clientSocketId] of webClients) {
          const clientSocket = io.sockets.sockets.get(clientSocketId);
          if (clientSocket) {
            clientSocket.emit('device_status_changed', {
              type: 'device_status_updated',
              deviceId: device.deviceId,
              status: 'online',
              message: '闲鱼脚本已停止，设备恢复在线',
              timestamp: new Date().toISOString()
            });
          }
        }

        console.log(`闲鱼设备状态已恢复为在线: ${device.deviceId}`);
      }
    });

    // 处理屏幕流数据
    socket.on('screen_frame', (data) => {
      const device = devices.get(socket.id);
      if (device) {
        // 转发屏幕帧到所有Web客户端
        for (const [clientSocketId] of webClients) {
          const clientSocket = io.sockets.sockets.get(clientSocketId);
          if (clientSocket) {
            clientSocket.emit('screen_frame', {
              deviceId: device.deviceId,
              frameData: data.frameData,
              timestamp: data.timestamp
            });
          }
        }
      }
    });

    // 处理心跳
    socket.on('heartbeat', () => {
      const device = devices.get(socket.id);
      if (device) {
        device.lastSeen = new Date();
        // 使用节流日志，避免频繁输出心跳信息
        throttledLog(`heartbeat_${device.deviceId}`, `设备心跳: ${device.deviceName} (${device.deviceId})`);
      }

      const webClient = webClients.get(socket.id);
      if (webClient) {
        webClient.lastSeen = new Date();
      }
    });
  });

  // 定期清理断开连接的设备记录
  setInterval(() => {
    const now = Date.now();
    const cleanupThreshold = 5 * 60 * 1000; // 5分钟

    for (const [deviceId, disconnectTime] of recentlyDisconnectedDevices) {
      if (now - disconnectTime > cleanupThreshold) {
        recentlyDisconnectedDevices.delete(deviceId);
        console.log(`清理过期的断开设备记录: ${deviceId}`);
      }
    }
  }, 60000); // 每分钟检查一次

  // 广播设备状态变化的工具函数
  function broadcastDeviceStatusChange(deviceId, newStatus, message = '') {
    const device = Array.from(devices.values()).find(d => d.deviceId === deviceId);
    if (device) {
      device.status = newStatus;
      device.lastActivity = new Date();

      const statusChange = {
        type: 'device_status_changed',
        deviceId: deviceId,
        deviceName: device.deviceName,
        status: newStatus,
        message: message,
        timestamp: new Date().toISOString()
      };

      // 广播到所有Web客户端
      for (const [clientSocketId] of webClients) {
        const clientSocket = io.sockets.sockets.get(clientSocketId);
        if (clientSocket) {
          clientSocket.emit('device_status_changed', statusChange);
        }
      }

      console.log(`📡 已广播设备状态变化: ${deviceId} -> ${newStatus}`);
    }
  }

  console.log('✅ WebSocket和静态文件模块设置完成');

  return {
    broadcastDeviceStatusChange
  };
}

module.exports = { setupServerWebSocket };
