#!/usr/bin/env node

/**
 * WebSocket长效连接测试脚本
 * 用于验证心跳机制、自动重连、单一客户端等功能
 */

const io = require('socket.io-client');
const readline = require('readline');

class WebSocketTester {
  constructor() {
    this.socket = null;
    this.isConnected = false;
    this.clientId = null;
    this.sessionId = null;
    this.reconnectAttempts = 0;
    this.heartbeatCount = 0;
    this.keepAliveCount = 0;
    this.startTime = null;
    this.lastPingTime = null;
    this.networkLatency = null;
    
    this.rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout
    });
  }

  generateClientId() {
    const timestamp = Date.now();
    const random = Math.random().toString(36).substr(2, 9);
    this.clientId = `test_${timestamp}_${random}`;
    console.log(`🔧 生成客户端ID: ${this.clientId}`);
  }

  async connect(serverUrl = 'http://localhost:3002') {
    if (this.socket && this.isConnected) {
      console.log('⚠️ 已经连接，无需重复连接');
      return;
    }

    this.generateClientId();
    console.log(`🔌 开始连接到 ${serverUrl}...`);
    this.startTime = Date.now();

    this.socket = io(serverUrl, {
      transports: ['websocket'],
      timeout: 30000,
      reconnection: false,
      forceNew: true,
      upgrade: false,
      autoConnect: true,
      pingTimeout: 120000,
      pingInterval: 30000,
      query: {
        clientId: this.clientId,
        clientType: 'test_client'
      }
    });

    this.setupEventHandlers();
  }

  setupEventHandlers() {
    // 连接成功
    this.socket.on('connect', () => {
      this.isConnected = true;
      const duration = Date.now() - this.startTime;
      console.log(`✅ 连接成功! 耗时: ${duration}ms`);
      
      // 注册为Web客户端
      this.socket.emit('web_client_connect', {
        userId: 'test_user',
        username: 'test_user',
        clientType: 'test_client',
        clientId: this.clientId,
        timestamp: new Date().toISOString(),
        userAgent: 'Node.js Test Client',
        url: 'test://websocket-tester'
      });

      this.startHeartbeat();
      this.startKeepAlive();
    });

    // 连接确认
    this.socket.on('connection_confirmed', (data) => {
      this.sessionId = data.sessionId;
      console.log(`📋 连接确认，会话ID: ${this.sessionId}`);
    });

    // 强制断开
    this.socket.on('force_disconnect', (data) => {
      console.log(`⚠️ 收到强制断开通知: ${data.message} (原因: ${data.reason})`);
    });

    // 断开连接
    this.socket.on('disconnect', (reason) => {
      this.isConnected = false;
      const duration = this.startTime ? Date.now() - this.startTime : 0;
      console.log(`🔌 连接断开: ${reason}, 连接时长: ${duration}ms`);
      
      // 自动重连
      if (reason !== 'io client disconnect') {
        this.reconnectAttempts++;
        console.log(`🔄 准备重连 (第${this.reconnectAttempts}次)...`);
        setTimeout(() => {
          this.connect();
        }, 3000);
      }
    });

    // Pong响应
    this.socket.on('pong', (data) => {
      if (this.lastPingTime) {
        this.networkLatency = Date.now() - this.lastPingTime;
        console.log(`🏓 网络延迟: ${this.networkLatency}ms`);
      }
    });

    // 心跳响应
    this.socket.on('heartbeat_response', (data) => {
      console.log(`💓 收到心跳响应: ${data.status}`);
    });

    // 连接错误
    this.socket.on('connect_error', (error) => {
      console.error(`❌ 连接错误: ${error.message}`);
    });
  }

  startHeartbeat() {
    setInterval(() => {
      if (this.socket && this.isConnected) {
        this.heartbeatCount++;
        this.socket.emit('heartbeat', {
          timestamp: new Date().toISOString(),
          clientId: this.clientId,
          clientType: 'test_client',
          pageVisible: true,
          connectionDuration: Date.now() - this.startTime,
          reconnectCount: this.reconnectAttempts
        });
        console.log(`💓 发送心跳 #${this.heartbeatCount}`);
      }
    }, 15000); // 15秒间隔
  }

  startKeepAlive() {
    setInterval(() => {
      if (this.socket && this.isConnected) {
        this.keepAliveCount++;
        this.socket.emit('keep_alive', {
          clientId: this.clientId,
          timestamp: Date.now(),
          pageVisible: true
        });
        console.log(`🔄 发送保活信号 #${this.keepAliveCount}`);
      }
    }, 30000); // 30秒间隔
  }

  sendPing() {
    if (this.socket && this.isConnected) {
      this.lastPingTime = Date.now();
      this.socket.emit('ping', { timestamp: this.lastPingTime });
      console.log('🏓 发送ping...');
    } else {
      console.log('⚠️ 未连接，无法发送ping');
    }
  }

  disconnect() {
    if (this.socket && this.isConnected) {
      console.log('🔌 主动断开连接...');
      this.socket.disconnect();
    } else {
      console.log('⚠️ 未连接，无需断开');
    }
  }

  getStats() {
    const duration = this.startTime ? Date.now() - this.startTime : 0;
    const stats = {
      isConnected: this.isConnected,
      clientId: this.clientId,
      sessionId: this.sessionId,
      connectionDuration: duration,
      reconnectAttempts: this.reconnectAttempts,
      heartbeatCount: this.heartbeatCount,
      keepAliveCount: this.keepAliveCount,
      networkLatency: this.networkLatency
    };
    
    console.log('\n📊 连接统计:');
    console.log(JSON.stringify(stats, null, 2));
    return stats;
  }

  showMenu() {
    console.log('\n=== WebSocket长效连接测试菜单 ===');
    console.log('1. 连接服务器');
    console.log('2. 断开连接');
    console.log('3. 发送ping测试延迟');
    console.log('4. 显示连接统计');
    console.log('5. 测试重复登录 (新建连接)');
    console.log('6. 退出');
    console.log('================================\n');
  }

  async start() {
    console.log('🚀 WebSocket长效连接测试工具启动');
    console.log('📝 此工具用于测试心跳机制、自动重连、单一客户端等功能\n');

    while (true) {
      this.showMenu();
      
      const choice = await this.askQuestion('请选择操作 (1-6): ');
      
      switch (choice.trim()) {
        case '1':
          const url = await this.askQuestion('服务器地址 (默认: http://localhost:3002): ');
          await this.connect(url.trim() || 'http://localhost:3002');
          break;
          
        case '2':
          this.disconnect();
          break;
          
        case '3':
          this.sendPing();
          break;
          
        case '4':
          this.getStats();
          break;
          
        case '5':
          console.log('🔄 创建新连接测试重复登录...');
          const newTester = new WebSocketTester();
          await newTester.connect();
          setTimeout(() => {
            newTester.disconnect();
          }, 5000);
          break;
          
        case '6':
          console.log('👋 退出测试工具');
          this.disconnect();
          this.rl.close();
          process.exit(0);
          break;
          
        default:
          console.log('❌ 无效选择，请重新输入');
      }
      
      // 等待一下再显示菜单
      await this.sleep(1000);
    }
  }

  askQuestion(question) {
    return new Promise((resolve) => {
      this.rl.question(question, resolve);
    });
  }

  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

// 启动测试工具
const tester = new WebSocketTester();
tester.start().catch(console.error);

// 处理进程退出
process.on('SIGINT', () => {
  console.log('\n👋 收到退出信号，正在清理...');
  tester.disconnect();
  process.exit(0);
});

process.on('SIGTERM', () => {
  console.log('\n👋 收到终止信号，正在清理...');
  tester.disconnect();
  process.exit(0);
});
