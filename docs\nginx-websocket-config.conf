# Nginx WebSocket长效连接配置示例
# 适用于群控系统的WebSocket代理配置

# 上游服务器配置
upstream websocket_backend {
    # 群控服务器地址和端口
    server 127.0.0.1:3002;
    
    # 保持连接配置
    keepalive 32;
    keepalive_requests 1000;
    keepalive_timeout 60s;
}

# HTTP服务器配置
server {
    listen 80;
    server_name your-domain.com;  # 替换为你的域名
    
    # 静态文件服务
    location / {
        proxy_pass http://websocket_backend;
        proxy_http_version 1.1;
        
        # 基本代理头
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # 长效连接配置
        proxy_read_timeout 300s;      # 5分钟读取超时
        proxy_send_timeout 300s;      # 5分钟发送超时
        proxy_connect_timeout 60s;    # 1分钟连接超时
        
        # 缓冲区配置
        proxy_buffering off;
        proxy_buffer_size 4k;
        proxy_buffers 8 4k;
        proxy_busy_buffers_size 8k;
    }
    
    # WebSocket代理配置
    location /socket.io/ {
        proxy_pass http://websocket_backend;
        proxy_http_version 1.1;
        
        # WebSocket升级头
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        
        # 基本代理头
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # 长效连接超时配置（关键配置）
        proxy_read_timeout 3600s;     # 1小时读取超时
        proxy_send_timeout 3600s;     # 1小时发送超时
        proxy_connect_timeout 60s;    # 1分钟连接超时
        
        # WebSocket特殊配置
        proxy_buffering off;          # 禁用缓冲
        proxy_cache off;              # 禁用缓存
        
        # 保持连接
        proxy_set_header Connection "keep-alive";
        proxy_http_version 1.1;
        
        # 防止代理超时
        proxy_ignore_client_abort on;
    }
    
    # API代理配置
    location /api/ {
        proxy_pass http://websocket_backend;
        proxy_http_version 1.1;
        
        # 基本代理头
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # API超时配置
        proxy_read_timeout 120s;
        proxy_send_timeout 120s;
        proxy_connect_timeout 30s;
        
        # 保持连接
        proxy_set_header Connection "keep-alive";
    }
    
    # 文件上传配置
    location /uploads/ {
        proxy_pass http://websocket_backend;
        proxy_http_version 1.1;
        
        # 基本代理头
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # 文件上传配置
        client_max_body_size 100M;   # 最大文件大小
        proxy_read_timeout 300s;     # 5分钟超时
        proxy_send_timeout 300s;
        proxy_connect_timeout 60s;
        
        # 上传缓冲配置
        proxy_request_buffering off;
        proxy_buffering off;
    }
}

# HTTPS服务器配置（推荐用于生产环境）
server {
    listen 443 ssl http2;
    server_name your-domain.com;  # 替换为你的域名
    
    # SSL证书配置
    ssl_certificate /path/to/your/certificate.crt;
    ssl_certificate_key /path/to/your/private.key;
    
    # SSL安全配置
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;
    
    # 静态文件服务
    location / {
        proxy_pass http://websocket_backend;
        proxy_http_version 1.1;
        
        # 基本代理头
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # 长效连接配置
        proxy_read_timeout 300s;
        proxy_send_timeout 300s;
        proxy_connect_timeout 60s;
        
        # 缓冲区配置
        proxy_buffering off;
        proxy_buffer_size 4k;
        proxy_buffers 8 4k;
        proxy_busy_buffers_size 8k;
    }
    
    # WebSocket代理配置（HTTPS）
    location /socket.io/ {
        proxy_pass http://websocket_backend;
        proxy_http_version 1.1;
        
        # WebSocket升级头
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        
        # 基本代理头
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # 长效连接超时配置（关键配置）
        proxy_read_timeout 3600s;     # 1小时读取超时
        proxy_send_timeout 3600s;     # 1小时发送超时
        proxy_connect_timeout 60s;    # 1分钟连接超时
        
        # WebSocket特殊配置
        proxy_buffering off;          # 禁用缓冲
        proxy_cache off;              # 禁用缓存
        
        # 保持连接
        proxy_set_header Connection "keep-alive";
        proxy_http_version 1.1;
        
        # 防止代理超时
        proxy_ignore_client_abort on;
    }
    
    # API代理配置
    location /api/ {
        proxy_pass http://websocket_backend;
        proxy_http_version 1.1;
        
        # 基本代理头
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # API超时配置
        proxy_read_timeout 120s;
        proxy_send_timeout 120s;
        proxy_connect_timeout 30s;
        
        # 保持连接
        proxy_set_header Connection "keep-alive";
    }
    
    # 文件上传配置
    location /uploads/ {
        proxy_pass http://websocket_backend;
        proxy_http_version 1.1;
        
        # 基本代理头
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # 文件上传配置
        client_max_body_size 100M;   # 最大文件大小
        proxy_read_timeout 300s;     # 5分钟超时
        proxy_send_timeout 300s;
        proxy_connect_timeout 60s;
        
        # 上传缓冲配置
        proxy_request_buffering off;
        proxy_buffering off;
    }
}

# HTTP重定向到HTTPS（可选）
server {
    listen 80;
    server_name your-domain.com;
    return 301 https://$server_name$request_uri;
}

# 全局配置建议
# 在nginx.conf的http块中添加以下配置：

# http {
#     # 长效连接配置
#     keepalive_timeout 65s;
#     keepalive_requests 1000;
#     
#     # 客户端配置
#     client_max_body_size 100M;
#     client_body_timeout 60s;
#     client_header_timeout 60s;
#     
#     # 发送配置
#     send_timeout 60s;
#     sendfile on;
#     tcp_nopush on;
#     tcp_nodelay on;
#     
#     # 缓冲区配置
#     proxy_buffering off;
#     proxy_buffer_size 4k;
#     proxy_buffers 8 4k;
#     proxy_busy_buffers_size 8k;
#     
#     # 日志配置
#     access_log /var/log/nginx/access.log;
#     error_log /var/log/nginx/error.log;
# }
