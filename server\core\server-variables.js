/**
 * 服务器变量声明模块 - 完整拆分版本
 * 包含所有全局变量、常量和数据结构声明
 * 对应原始文件中所有变量声明的完整内容，包含以下功能：
 * - 全局变量声明
 * - 数据结构初始化
 * - 常量定义
 * - 配置变量
 * - 状态管理变量
 * - 缓存和存储变量
 * 以及所有相关的变量声明
 */

// 服务器变量声明模块设置函数
async function setupServerVariables() {
  console.log('🔧 设置服务器变量声明模块...');

  // 小红书活跃任务管理 (原始文件第1863行)
  const xiaohongshuActiveTasks = new Map();

  // 闲鱼活跃任务管理
  const xianyuActiveTasks = new Map();

  // 任务ID生成器
  let taskIdCounter = 1;

  // 生成唯一任务ID
  function generateTaskId() {
    return `task_${Date.now()}_${taskIdCounter++}`;
  }

  // 日志ID生成器
  let logIdCounter = 1;

  // 生成唯一日志ID
  function generateLogId() {
    return `log_${Date.now()}_${logIdCounter++}`;
  }

  // 设备状态常量
  const DEVICE_STATUS = {
    ONLINE: 'online',
    OFFLINE: 'offline',
    BUSY: 'busy',
    ERROR: 'error'
  };

  // 执行状态常量
  const EXECUTION_STATUS = {
    PENDING: 'pending',
    RUNNING: 'running',
    COMPLETED: 'completed',
    FAILED: 'failed',
    STOPPED: 'stopped'
  };

  // 功能类型常量
  const FUNCTION_TYPES = {
    PROFILE: 'profile',
    GROUP_CHAT: 'groupChat',
    SEARCH_GROUP_CHAT: 'searchGroupChat',
    GROUP_MESSAGE: 'groupMessage',
    ARTICLE_COMMENT: 'articleComment',
    UID_MESSAGE: 'uidMessage',
    UID_FILE_MESSAGE: 'uidFileMessage',
    VIDEO_PUBLISH: 'videoPublish'
  };

  // 小红书功能类型映射
  const XIAOHONGSHU_FUNCTION_MAP = {
    [FUNCTION_TYPES.PROFILE]: '修改资料',
    [FUNCTION_TYPES.GROUP_CHAT]: '搜索群聊加群发消息',
    [FUNCTION_TYPES.SEARCH_GROUP_CHAT]: '搜索群聊',
    [FUNCTION_TYPES.GROUP_MESSAGE]: '循环群发消息',
    [FUNCTION_TYPES.ARTICLE_COMMENT]: '搜索文章评论',
    [FUNCTION_TYPES.UID_MESSAGE]: 'UID私信',
    [FUNCTION_TYPES.UID_FILE_MESSAGE]: 'UID文件私信',
    [FUNCTION_TYPES.VIDEO_PUBLISH]: '视频发布'
  };

  // 闲鱼功能类型
  const XIANYU_FUNCTION_TYPES = {
    KEYWORD_MESSAGE: 'keywordMessage'
  };

  // 文件上传配置
  const UPLOAD_CONFIG = {
    MAX_FILE_SIZE: 2 * 1024 * 1024 * 1024, // 2GB
    MAX_FILE_COUNT: 1000,
    ALLOWED_VIDEO_TYPES: ['video/mp4', 'video/avi', 'video/mov', 'video/wmv', 'video/flv'],
    ALLOWED_IMAGE_TYPES: ['image/jpeg', 'image/png', 'image/gif', 'image/bmp'],
    ALLOWED_TEXT_TYPES: ['text/plain', 'text/csv']
  };

  // 性能监控配置
  const PERFORMANCE_CONFIG = {
    MEMORY_WARNING_THRESHOLD: 500, // MB
    SLOW_REQUEST_THRESHOLD: 5000, // ms
    CLEANUP_INTERVAL: 10 * 60 * 1000, // 10分钟
    MONITOR_INTERVAL: 30 * 1000, // 30秒
    MAX_LOG_ENTRIES: 1000,
    MAX_MEMORY_ENTRIES: 100,
    MAX_RESPONSE_TIME_ENTRIES: 1000,
    MAX_CONNECTION_ENTRIES: 100
  };

  // 数据库配置
  const DATABASE_CONFIG = {
    CLEANUP_INTERVAL: 5 * 60 * 1000, // 5分钟
    EXPIRED_TASK_THRESHOLD: 10 * 60 * 1000, // 10分钟
    MAX_RETRY_ATTEMPTS: 3,
    CONNECTION_TIMEOUT: 60000, // 60秒
    ACQUIRE_TIMEOUT: 60000, // 60秒
    IDLE_TIMEOUT: 300000 // 5分钟
  };

  // WebSocket配置 - 长效连接优化
  const WEBSOCKET_CONFIG = {
    PING_TIMEOUT: 120000, // 120秒（2分钟）
    PING_INTERVAL: 30000, // 30秒
    MAX_HTTP_BUFFER_SIZE: 1e8, // 100MB
    TRANSPORTS: ['websocket', 'polling'],
    // 长效连接配置
    UPGRADE_TIMEOUT: 30000, // 30秒升级超时
    CONNECT_TIMEOUT: 30000, // 30秒连接超时
    HEARTBEAT_TIMEOUT: 60000, // 60秒心跳超时
    HEARTBEAT_INTERVAL: 25000, // 25秒心跳间隔
    CORS: {
      origin: "*",
      methods: ["GET", "POST"],
      credentials: true
    }
  };

  // 设备重连配置
  const RECONNECTION_CONFIG = {
    COOLDOWN_PERIOD: 10000, // 10秒冷却期
    MAX_RECONNECTION_ATTEMPTS: 5,
    RECONNECTION_DELAY: 2000, // 2秒
    RECONNECTION_DELAY_MAX: 30000, // 30秒
    RANDOMIZATION_FACTOR: 0.5
  };

  // 脚本执行配置
  const SCRIPT_CONFIG = {
    DEFAULT_TIMEOUT: 300000, // 5分钟
    MAX_CONCURRENT_EXECUTIONS: 10,
    RETRY_ATTEMPTS: 3,
    RETRY_DELAY: 5000, // 5秒
    PROGRESS_UPDATE_INTERVAL: 1000 // 1秒
  };

  // 日志配置
  const LOG_CONFIG = {
    MAX_LOG_SIZE: 1000,
    LOG_RETENTION_DAYS: 7,
    LOG_LEVEL: process.env.LOG_LEVEL || 'info',
    ENABLE_DEBUG: process.env.NODE_ENV === 'development'
  };

  // 缓存配置
  const CACHE_CONFIG = {
    DEFAULT_TTL: 300000, // 5分钟
    MAX_CACHE_SIZE: 1000,
    CLEANUP_INTERVAL: 60000 // 1分钟
  };

  // 简单缓存实现
  const cache = new Map();
  const cacheTimestamps = new Map();

  // 缓存操作函数
  function setCache(key, value, ttl = CACHE_CONFIG.DEFAULT_TTL) {
    cache.set(key, value);
    cacheTimestamps.set(key, Date.now() + ttl);
  }

  function getCache(key) {
    const timestamp = cacheTimestamps.get(key);
    if (!timestamp || Date.now() > timestamp) {
      cache.delete(key);
      cacheTimestamps.delete(key);
      return null;
    }
    return cache.get(key);
  }

  function clearCache(key) {
    cache.delete(key);
    cacheTimestamps.delete(key);
  }

  function clearAllCache() {
    cache.clear();
    cacheTimestamps.clear();
  }

  // 定期清理过期缓存
  const cacheCleanupInterval = setInterval(() => {
    const now = Date.now();
    for (const [key, timestamp] of cacheTimestamps) {
      if (now > timestamp) {
        cache.delete(key);
        cacheTimestamps.delete(key);
      }
    }
  }, CACHE_CONFIG.CLEANUP_INTERVAL);

  console.log('✅ 服务器变量声明模块设置完成');

  // 返回所有变量和函数供其他模块使用
  return {
    // 任务管理
    xiaohongshuActiveTasks,
    xianyuActiveTasks,
    generateTaskId,
    generateLogId,

    // 常量
    DEVICE_STATUS,
    EXECUTION_STATUS,
    FUNCTION_TYPES,
    XIAOHONGSHU_FUNCTION_MAP,
    XIANYU_FUNCTION_TYPES,

    // 配置
    UPLOAD_CONFIG,
    PERFORMANCE_CONFIG,
    DATABASE_CONFIG,
    WEBSOCKET_CONFIG,
    RECONNECTION_CONFIG,
    SCRIPT_CONFIG,
    LOG_CONFIG,
    CACHE_CONFIG,

    // 缓存功能
    setCache,
    getCache,
    clearCache,
    clearAllCache,
    cacheCleanupInterval
  };
}

module.exports = { setupServerVariables };
