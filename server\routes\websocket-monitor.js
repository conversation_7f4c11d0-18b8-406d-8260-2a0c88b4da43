/**
 * WebSocket连接监控API
 * 提供连接状态、统计信息和健康检查功能
 */

const express = require('express');
const router = express.Router();

// WebSocket连接监控模块设置函数
function setupWebSocketMonitor(coreData, authData) {
  const { devices, webClients, throttledLog } = coreData;
  const { authenticateToken } = authData;

  // 获取连接统计信息
  router.get('/stats', authenticateToken, (req, res) => {
    try {
      const now = new Date();
      
      // 设备统计
      const deviceStats = {
        total: devices.size,
        online: 0,
        offline: 0,
        byStatus: {},
        connections: []
      };

      for (const [socketId, device] of devices) {
        const status = device.status || 'unknown';
        deviceStats.byStatus[status] = (deviceStats.byStatus[status] || 0) + 1;
        
        if (status === 'online') {
          deviceStats.online++;
        } else {
          deviceStats.offline++;
        }

        deviceStats.connections.push({
          socketId,
          deviceId: device.deviceId,
          deviceName: device.deviceName,
          status: device.status,
          connectedAt: device.connectedAt,
          lastSeen: device.lastSeen,
          ipAddress: device.deviceInfo?.ipAddress,
          connectionDuration: now - new Date(device.connectedAt)
        });
      }

      // Web客户端统计
      const webClientStats = {
        total: webClients.size,
        authenticated: 0,
        anonymous: 0,
        connections: []
      };

      for (const [socketId, client] of webClients) {
        if (client.userId && client.userId !== 'anonymous' && !client.userId.startsWith('anonymous_')) {
          webClientStats.authenticated++;
        } else {
          webClientStats.anonymous++;
        }

        webClientStats.connections.push({
          socketId,
          userId: client.userId,
          clientId: client.clientId,
          clientType: client.clientType,
          connectedAt: client.connectedAt,
          lastSeen: client.lastSeen,
          connectionDuration: now - new Date(client.connectedAt),
          heartbeatData: client.heartbeatData,
          userAgent: client.userAgent,
          url: client.url
        });
      }

      // 系统统计
      const systemStats = {
        uptime: process.uptime(),
        memory: process.memoryUsage(),
        timestamp: now.toISOString()
      };

      res.json({
        success: true,
        data: {
          devices: deviceStats,
          webClients: webClientStats,
          system: systemStats
        }
      });

    } catch (error) {
      console.error('获取连接统计失败:', error);
      res.status(500).json({
        success: false,
        message: '获取连接统计失败',
        error: error.message
      });
    }
  });

  // 获取特定客户端的详细信息
  router.get('/client/:clientId', authenticateToken, (req, res) => {
    try {
      const { clientId } = req.params;
      
      // 查找Web客户端
      let clientInfo = null;
      for (const [socketId, client] of webClients) {
        if (client.clientId === clientId) {
          clientInfo = {
            socketId,
            ...client,
            connectionDuration: Date.now() - new Date(client.connectedAt)
          };
          break;
        }
      }

      if (!clientInfo) {
        return res.status(404).json({
          success: false,
          message: '客户端未找到'
        });
      }

      res.json({
        success: true,
        data: clientInfo
      });

    } catch (error) {
      console.error('获取客户端信息失败:', error);
      res.status(500).json({
        success: false,
        message: '获取客户端信息失败',
        error: error.message
      });
    }
  });

  // 强制断开特定客户端
  router.post('/disconnect/:clientId', authenticateToken, (req, res) => {
    try {
      const { clientId } = req.params;
      const { reason = 'admin_disconnect', message = '管理员断开连接' } = req.body;
      
      // 查找并断开Web客户端
      let disconnected = false;
      for (const [socketId, client] of webClients) {
        if (client.clientId === clientId) {
          const io = req.app.get('io');
          const socket = io.sockets.sockets.get(socketId);
          
          if (socket) {
            socket.emit('force_disconnect', {
              reason,
              message,
              timestamp: new Date().toISOString()
            });
            socket.disconnect(true);
            disconnected = true;
            
            console.log(`管理员断开客户端连接: ${clientId} (${client.userId})`);
          }
          break;
        }
      }

      if (!disconnected) {
        return res.status(404).json({
          success: false,
          message: '客户端未找到或已断开'
        });
      }

      res.json({
        success: true,
        message: '客户端已断开'
      });

    } catch (error) {
      console.error('断开客户端失败:', error);
      res.status(500).json({
        success: false,
        message: '断开客户端失败',
        error: error.message
      });
    }
  });

  // 获取连接健康状态
  router.get('/health', (req, res) => {
    try {
      const now = Date.now();
      const healthThreshold = 60000; // 60秒内有活动认为健康
      
      let healthyDevices = 0;
      let unhealthyDevices = 0;
      let healthyClients = 0;
      let unhealthyClients = 0;

      // 检查设备健康状态
      for (const [socketId, device] of devices) {
        const lastSeenTime = new Date(device.lastSeen).getTime();
        if (now - lastSeenTime < healthThreshold) {
          healthyDevices++;
        } else {
          unhealthyDevices++;
        }
      }

      // 检查Web客户端健康状态
      for (const [socketId, client] of webClients) {
        const lastSeenTime = new Date(client.lastSeen).getTime();
        if (now - lastSeenTime < healthThreshold) {
          healthyClients++;
        } else {
          unhealthyClients++;
        }
      }

      const overallHealth = (unhealthyDevices === 0 && unhealthyClients === 0) ? 'healthy' : 
                           (unhealthyDevices + unhealthyClients < (healthyDevices + healthyClients) / 2) ? 'warning' : 'critical';

      res.json({
        success: true,
        data: {
          status: overallHealth,
          timestamp: new Date().toISOString(),
          devices: {
            healthy: healthyDevices,
            unhealthy: unhealthyDevices,
            total: devices.size
          },
          webClients: {
            healthy: healthyClients,
            unhealthy: unhealthyClients,
            total: webClients.size
          },
          thresholds: {
            healthyThreshold: healthThreshold
          }
        }
      });

    } catch (error) {
      console.error('获取健康状态失败:', error);
      res.status(500).json({
        success: false,
        message: '获取健康状态失败',
        error: error.message
      });
    }
  });

  // 获取连接事件日志（如果有的话）
  router.get('/events', authenticateToken, (req, res) => {
    try {
      const { limit = 50, type, clientId } = req.query;
      
      // 这里可以从日志系统或内存中获取事件
      // 目前返回模拟数据，实际实现需要根据具体的日志存储方案
      const events = [
        {
          id: 1,
          type: 'connection_established',
          clientId: 'test_client_123',
          timestamp: new Date().toISOString(),
          data: { reconnectAttempts: 0 }
        },
        {
          id: 2,
          type: 'heartbeat_timeout',
          clientId: 'test_client_456',
          timestamp: new Date(Date.now() - 60000).toISOString(),
          data: { timeout: 5000 }
        }
      ];

      res.json({
        success: true,
        data: {
          events: events.slice(0, parseInt(limit)),
          total: events.length
        }
      });

    } catch (error) {
      console.error('获取连接事件失败:', error);
      res.status(500).json({
        success: false,
        message: '获取连接事件失败',
        error: error.message
      });
    }
  });

  return router;
}

module.exports = { setupWebSocketMonitor };
