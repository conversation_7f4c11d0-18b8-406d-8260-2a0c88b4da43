import io from 'socket.io-client'
import store from '@/store'
import { getWebSocketUrl } from '@/utils/serverConfig'

// 纯WebSocket通信管理器
class WebSocketManager {
  constructor() {
    this.socket = null
    this.isConnected = false
    this.reconnectAttempts = 0
    this.maxReconnectAttempts = 50 // 增加重连次数
    this.reconnectDelay = 1000 // 初始重连延迟1秒
    this.maxReconnectDelay = 30000 // 最大重连延迟30秒
    this.eventHandlers = new Map()
    this.isInitialized = false
    this.reconnectTimer = null
    this.heartbeatTimer = null
    this.heartbeatInterval = 10000 // 10秒心跳间隔（更频繁的心跳）
    this.connectionPromise = null
    this.lastHeartbeatTime = null
    this.heartbeatTimeout = 5000 // 心跳超时时间5秒
    this.connectionCheckTimer = null
    this.connectionCheckInterval = 15000 // 15秒检查一次连接状态
    this.forceReconnectTimer = null
    this.forceReconnectInterval = 45000 // 45秒强制检查一次连接
    this.visibilityChangeHandler = null
    this.beforeUnloadHandler = null
    this.sessionId = null // 会话ID，用于单一客户端控制
    this.clientId = null // 客户端唯一标识
    this.isPageClosing = false // 页面关闭标志
    this.keepAliveTimer = null // 保活定时器
    this.keepAliveInterval = 30000 // 30秒保活间隔
    this.isPageVisible = true
    this.connectionStartTime = null // 连接开始时间
    this.heartbeatTimeoutTimer = null // 心跳超时定时器
    this.connectionEvents = [] // 连接事件记录
    this.maxConnectionEvents = 100 // 最大事件记录数
    this.lastPingTime = null // 最后ping时间
    this.lastPongTime = null // 最后pong时间
    this.networkLatency = null // 网络延迟

    // 生成客户端唯一标识
    this.generateClientId()

    // 监听页面可见性变化
    this.setupVisibilityListener()
  }

  // 生成客户端唯一标识
  generateClientId() {
    // 使用用户ID + 时间戳 + 随机数生成唯一客户端ID
    const user = store.getters['auth/user']
    const userId = user ? user.id : 'anonymous'
    const timestamp = Date.now()
    const random = Math.random().toString(36).substr(2, 9)
    this.clientId = `${userId}_${timestamp}_${random}`
    console.log('🔧 [WebSocketManager] 生成客户端ID:', this.clientId)
  }

  // 设置页面可见性监听
  setupVisibilityListener() {
    if (typeof document !== 'undefined') {
      this.visibilityChangeHandler = () => {
        this.isPageVisible = !document.hidden

        if (!document.hidden && this.isInitialized) {
          console.log('🔧 [WebSocketManager] 页面重新可见，检查连接状态')
          // 页面重新可见时，重新生成客户端ID以确保唯一性
          this.generateClientId()
          setTimeout(() => {
            this.checkConnectionAfterVisibilityChange()
          }, 1000) // 延迟1秒检查，确保页面完全激活
        } else if (document.hidden) {
          console.log('🔧 [WebSocketManager] 页面变为不可见，保持连接但减少活动')
        }
      }

      document.addEventListener('visibilitychange', this.visibilityChangeHandler)

      // 监听页面卸载事件 - 只在真正关闭时断开
      this.beforeUnloadHandler = (event) => {
        console.log('🔧 [WebSocketManager] 页面即将卸载，发送关闭通知')
        this.isPageClosing = true
        // 发送页面关闭通知给服务器，但不立即断开连接
        if (this.socket && this.isConnected) {
          this.socket.emit('client_closing', {
            clientId: this.clientId,
            reason: 'page_unload',
            timestamp: Date.now()
          })
        }
      }

      window.addEventListener('beforeunload', this.beforeUnloadHandler)

      // 监听页面隐藏事件（更精确的页面切换检测）
      this.pageHideHandler = () => {
        console.log('🔧 [WebSocketManager] 页面隐藏，保持连接')
        // 页面隐藏时不断开连接，保持后台连接
      }

      window.addEventListener('pagehide', this.pageHideHandler)
    }
  }

  // 页面可见性变化后检查连接
  checkConnectionAfterVisibilityChange() {
    if (!this.isConnected || !this.socket || this.socket.disconnected) {
      console.log('🔄 [WebSocketManager] 页面重新可见时发现连接已断开，尝试重连')
      this.handleConnectionError(new Error('页面重新可见时连接已断开'))
    } else {
      console.log('✅ [WebSocketManager] 页面重新可见时连接正常')
      // 发送一个心跳确认连接
      this.socket.emit('heartbeat', {
        timestamp: new Date().toISOString(),
        clientType: 'websocket_manager',
        reason: 'visibility_check'
      })
    }
  }

  // 当页面可见时安排重连
  scheduleReconnectWhenVisible() {
    console.log('🔄 [WebSocketManager] 安排页面可见时重连')

    // 如果页面已经可见，立即重连
    if (this.isPageVisible) {
      console.log('🔄 [WebSocketManager] 页面已可见，立即重连')
      this.scheduleReconnect()
      return
    }

    // 否则等待页面变为可见
    const checkVisibility = () => {
      if (this.isPageVisible && this.isInitialized) {
        console.log('🔄 [WebSocketManager] 页面变为可见，开始重连')
        this.scheduleReconnect()
      } else if (this.isInitialized) {
        // 继续等待
        setTimeout(checkVisibility, 2000)
      }
    }

    setTimeout(checkVisibility, 2000)
  }

  // 初始化WebSocket连接
  async init() {
    // 允许未认证用户也能建立连接，用于接收设备事件
    const isAuthenticated = store.getters['auth/isAuthenticated']
    console.log('🔧 [WebSocketManager] 初始化WebSocket连接...', { isAuthenticated })

    if (this.isInitialized && this.isConnected) {
      console.log('🔧 [WebSocketManager] WebSocket管理器已初始化且连接正常')
      return this.connectionPromise
    }

    console.log('🔧 [WebSocketManager] 初始化WebSocket管理器...')
    this.isInitialized = true

    // 如果已有连接Promise在进行中，返回它
    if (this.connectionPromise) {
      console.log('🔧 [WebSocketManager] 连接Promise已存在，返回现有Promise')
      return this.connectionPromise
    }

    this.connectionPromise = this.connect()
    return this.connectionPromise
  }

  // 建立WebSocket连接
  async connect() {
    try {
      console.log('🔧 [WebSocketManager] 建立WebSocket连接...')

      // 清理之前的连接
      this.cleanup()

      // 获取服务器地址
      const serverUrl = getWebSocketUrl()
      console.log('🔧 [WebSocketManager] 连接到WebSocket服务器:', serverUrl || '使用代理模式')

      // 创建Socket连接配置 - 长效连接优化
      const socketOptions = {
        transports: ['websocket'], // 只使用WebSocket，不降级到polling
        timeout: 30000, // 增加超时时间到30秒
        reconnection: false, // 手动控制重连
        forceNew: true,
        upgrade: false, // 禁用传输升级，直接使用WebSocket
        autoConnect: true, // 自动连接
        pingTimeout: 120000, // ping超时时间120秒（2分钟）
        pingInterval: 30000, // ping间隔30秒
        // 长效连接配置
        rememberUpgrade: false,
        maxHttpBufferSize: 1e8, // 100MB缓冲区
        // 添加客户端标识
        query: {
          clientId: this.clientId,
          clientType: 'web_client'
        }
      }

      // 根据是否有serverUrl决定连接方式
      if (serverUrl) {
        this.socket = io(serverUrl, socketOptions)
      } else {
        // 使用代理模式（开发环境）
        this.socket = io(socketOptions)
      }

      // 设置连接超时
      const connectTimeout = setTimeout(() => {
        console.error('WebSocket连接超时')
        this.handleConnectionError(new Error('连接超时'))
      }, 15000)

      return new Promise((resolve, reject) => {
        this.socket.on('connect', () => {
          clearTimeout(connectTimeout)
          console.log('✅ [WebSocketManager] WebSocket连接成功')
          this.isConnected = true
          this.reconnectAttempts = 0
          this.reconnectDelay = 1000
          this.connectionStartTime = Date.now()

          // 记录连接成功事件
          this.recordConnectionEvent('connection_established', {
            attempt: this.reconnectAttempts,
            duration: this.connectionStartTime - (this.lastConnectAttemptTime || this.connectionStartTime)
          })

          // 设置事件处理器
          this.setupEventHandlers()

          // 启动心跳
          this.startHeartbeat()

          // 启动连接状态检查
          this.startConnectionCheck()

          // 启动强制重连检查
          this.startForceReconnectCheck()

          // 启动保活机制
          this.startKeepAlive()

          // 显示连接成功提示
          this.showConnectionNotification('success', '连接已建立', '与服务器的连接已成功建立')

          // 通知store连接成功
          store.dispatch('socket/connect', this.socket)

          // 直接设置连接状态为true，因为socket已经连接
          store.commit('socket/SET_CONNECTED', true)
          store.commit('socket/SET_SOCKET', this.socket)

          // 触发连接成功事件
          this.emitEvent('connection_established', { type: 'websocket' })

          // 注册Web客户端（带客户端ID）
          this.registerWebClient()

          // 连接成功后立即请求设备状态同步
          setTimeout(() => {
            this.requestDeviceStatusSync()
          }, 1000)

          resolve()
        })

        this.socket.on('connect_error', (error) => {
          clearTimeout(connectTimeout)
          console.error('❌ [WebSocketManager] WebSocket连接失败:', error.message)
          this.handleConnectionError(error)
          reject(error)
        })

        this.socket.on('disconnect', (reason) => {
          console.log('🔌 [WebSocketManager] WebSocket连接断开:', reason)
          this.isConnected = false
          this.stopHeartbeat()
          this.stopConnectionCheck()
          this.stopForceReconnectCheck()
          this.stopKeepAlive()

          // 记录断开事件
          this.recordConnectionEvent('connection_lost', {
            reason,
            duration: this.connectionStartTime ? Date.now() - this.connectionStartTime : 0,
            reconnectAttempts: this.reconnectAttempts,
            pageVisible: this.isPageVisible,
            networkLatency: this.networkLatency
          })

          // 清除心跳超时检测
          if (this.heartbeatTimeoutTimer) {
            clearTimeout(this.heartbeatTimeoutTimer)
            this.heartbeatTimeoutTimer = null
          }

          // 通知store连接断开
          store.dispatch('socket/disconnect')
          store.commit('socket/SET_CONNECTED', false)

          // 触发断开连接事件
          this.emitEvent('connection_lost', { reason })

          // 显示断开提示
          this.showDisconnectionNotification(reason)

          // 更智能的重连策略 - 区分真正的断开和页面切换
          const isClientDisconnect = reason === 'io client disconnect' || reason === 'client namespace disconnect'
          const isPageVisible = this.isPageVisible
          const isTransportClose = reason === 'transport close'
          const isServerDisconnect = reason === 'io server disconnect'

          // 如果是客户端主动断开，但页面仍然可见，可能是组件重新加载，应该重连
          const shouldReconnect = this.isInitialized &&
                                 !isClientDisconnect &&
                                 !this.isPageClosing &&
                                 !isServerDisconnect &&
                                 (isPageVisible || !isTransportClose)

          if (shouldReconnect) {
            console.log('🔄 [WebSocketManager] 检测到连接断开，准备重连...', {
              reason,
              pageVisible: isPageVisible,
              isClientDisconnect
            })

            // 根据断开原因调整重连策略
            let reconnectDelay = this.calculateReconnectDelay(reason, isPageVisible)

            setTimeout(() => {
              if (this.isInitialized && !this.isPageClosing) {
                this.scheduleReconnect()
              }
            }, reconnectDelay)
          } else {
            console.log('🔌 [WebSocketManager] 不进行重连', {
              reason,
              isInitialized: this.isInitialized,
              isClientDisconnect,
              pageVisible: isPageVisible
            })
          }
        })
      })

    } catch (error) {
      console.error('❌ [WebSocketManager] WebSocket连接异常:', error)
      this.handleConnectionError(error)
      throw error
    }
  }

  // 设置WebSocket事件处理器
  setupEventHandlers() {
    if (!this.socket) return

    // 设备列表更新
    this.socket.on('devices_list', (devices) => {
      console.log('WebSocket收到设备列表:', devices)
      this.emitEvent('devices_list', devices)
    })

    // 设备状态变化
    this.socket.on('device_status_changed', (data) => {
      console.log('WebSocket收到设备状态变化:', data)
      this.emitEvent('device_status_changed', data)
    })

    // 设备状态更新
    this.socket.on('device_status_update', (data) => {
      console.log('WebSocket收到设备状态更新:', data)
      this.emitEvent('device_status_update', data)

      // 立即更新store中的设备状态，确保状态同步
      if (store && data.deviceId) {
        store.commit('device/UPDATE_DEVICE', {
          deviceId: data.deviceId,
          updates: {
            status: data.status,
            last_seen: data.lastSeen || new Date().toISOString()
          }
        })
        console.log(`已更新store中设备 ${data.deviceId} 状态为: ${data.status}`)
      }
    })

    // 设备状态变化（健康检查等）
    this.socket.on('device_status_changed', (data) => {
      console.log('WebSocket收到设备状态变化:', data)
      this.emitEvent('device_status_changed', data)

      // 立即更新store中的设备状态
      if (store && data.deviceId) {
        const newStatus = data.type === 'device_health_warning' ? 'offline' : 'online'
        store.commit('device/UPDATE_DEVICE', {
          deviceId: data.deviceId,
          updates: {
            status: newStatus,
            last_seen: data.lastSeen || new Date().toISOString()
          }
        })
        console.log(`健康检查：已更新store中设备 ${data.deviceId} 状态为: ${newStatus}`)
      }
    })

    // 小红书状态更新
    this.socket.on('xiaohongshu_status_update', (data) => {
      console.log('WebSocket收到小红书状态更新:', data)
      this.emitEvent('xiaohongshu_status_update', data)
    })

    // 小红书批量停止相关事件
    this.socket.on('xiaohongshu_function_tasks_stopped', (data) => {
      console.log('WebSocket收到小红书功能任务停止:', data)
      this.emitEvent('xiaohongshu_function_tasks_stopped', data)
    })

    this.socket.on('xiaohongshu_force_refresh_vuex', (data) => {
      console.log('WebSocket收到小红书强制刷新Vuex:', data)
      this.emitEvent('xiaohongshu_force_refresh_vuex', data)
    })

    this.socket.on('xiaohongshu_clear_function_state', (data) => {
      console.log('WebSocket收到小红书清理功能状态:', data)
      this.emitEvent('xiaohongshu_clear_function_state', data)
    })

    // 小红书其他事件
    this.socket.on('xiaohongshu_all_tasks_stopped', (data) => {
      console.log('WebSocket收到小红书所有任务停止:', data)
      this.emitEvent('xiaohongshu_all_tasks_stopped', data)
    })

    this.socket.on('xiaohongshu_task_update', (data) => {
      console.log('WebSocket收到小红书任务更新:', data)
      this.emitEvent('xiaohongshu_task_update', data)
    })

    this.socket.on('xiaohongshu_debug_log', (data) => {
      console.log('WebSocket收到小红书调试日志:', data)
      this.emitEvent('xiaohongshu_debug_log', data)
    })

    this.socket.on('xiaohongshu_execution_completed', (data) => {
      console.log('WebSocket收到小红书执行完成:', data)
      this.emitEvent('xiaohongshu_execution_completed', data)
    })

    // 小红书实时状态
    this.socket.on('xiaohongshu_realtime_status', (data) => {
      console.log('WebSocket收到小红书实时状态:', data)
      this.emitEvent('xiaohongshu_realtime_status', data)
    })

    // 闲鱼实时状态
    this.socket.on('xianyu_realtime_status', (data) => {
      console.log('WebSocket收到闲鱼实时状态:', data)
      this.emitEvent('xianyu_realtime_status', data)
    })

    // 服务器关闭通知
    this.socket.on('server_shutdown', (data) => {
      console.log('收到服务器关闭通知:', data)
      this.emitEvent('server_shutdown', data)
      this.isConnected = false
      this.isInitialized = false
    })

    // 心跳响应
    this.socket.on('pong', () => {
      console.log('💓 [WebSocketManager] 收到pong心跳响应')
      this.lastHeartbeatTime = Date.now()
    })

    // 服务器心跳响应
    this.socket.on('heartbeat_response', (data) => {
      console.log('💓 [WebSocketManager] 收到服务器心跳响应:', data)
      this.lastHeartbeatTime = Date.now()
    })

    // 连接错误处理
    this.socket.on('connect_error', (error) => {
      console.error('❌ [WebSocketManager] 连接错误:', error)
      this.handleConnectionError(error)
    })

    // 重连错误处理
    this.socket.on('reconnect_error', (error) => {
      console.error('❌ [WebSocketManager] 重连错误:', error)
      this.handleConnectionError(error)
    })

    // 连接确认事件
    this.socket.on('connection_confirmed', (data) => {
      console.log('✅ [WebSocketManager] 收到连接确认:', data)
      this.sessionId = data.sessionId
      this.emitEvent('connection_confirmed', data)
    })

    // 强制断开事件
    this.socket.on('force_disconnect', (data) => {
      console.log('⚠️ [WebSocketManager] 收到强制断开通知:', data)
      this.recordConnectionEvent('force_disconnect', data)

      // 显示用户友好的提示
      if (data.reason === 'duplicate_login') {
        this.showConnectionNotification('warning', '重复登录', '检测到账号在其他地方登录，当前连接将被断开')
        this.emitEvent('duplicate_login', data)
      }
      // 不尝试重连，因为这是服务器主动断开
      this.isInitialized = false
    })

    // Pong响应处理
    this.socket.on('pong', (data) => {
      this.lastPongTime = Date.now()
      if (this.lastPingTime) {
        this.networkLatency = this.lastPongTime - this.lastPingTime
        console.log(`🏓 [WebSocketManager] 网络延迟: ${this.networkLatency}ms`)
      }

      // 清除心跳超时检测
      if (this.heartbeatTimeoutTimer) {
        clearTimeout(this.heartbeatTimeoutTimer)
        this.heartbeatTimeoutTimer = null
      }
    })

    // 心跳响应处理
    this.socket.on('heartbeat_response', (data) => {
      console.log('💓 [WebSocketManager] 收到心跳响应:', data)
      this.recordConnectionEvent('heartbeat_response', data)
    })

    // 测试事件
    this.socket.on('test_event', (data) => {
      console.log('收到测试事件:', data)
      this.emitEvent('test_event', data)
    })
  }

  // 启动心跳
  startHeartbeat() {
    this.stopHeartbeat()
    console.log('🔧 [WebSocketManager] 启动心跳机制，间隔:', this.heartbeatInterval + 'ms')

    // 立即发送一次心跳
    this.sendHeartbeat()

    this.heartbeatTimer = setInterval(() => {
      this.sendHeartbeat()
    }, this.heartbeatInterval)
  }

  // 发送心跳
  sendHeartbeat() {
    if (this.socket && this.isConnected && !this.socket.disconnected) {
      try {
        this.lastHeartbeatTime = Date.now()

        // 发送增强心跳信息
        this.socket.emit('heartbeat', {
          timestamp: new Date().toISOString(),
          clientId: this.clientId,
          clientType: 'websocket_manager',
          pageVisible: this.isPageVisible,
          connectionDuration: Date.now() - (this.connectionStartTime || Date.now()),
          reconnectCount: this.reconnectAttempts,
          userAgent: navigator.userAgent,
          url: window.location.href
        })

        // 发送ping用于延迟检测
        this.socket.emit('ping', { timestamp: Date.now() })

        // 根据页面可见性调整日志频率
        if (this.isPageVisible) {
          console.log('💓 [WebSocketManager] 发送心跳（页面可见）')
        } else {
          // 页面不可见时减少日志输出
          if (Date.now() % 60000 < this.heartbeatInterval) { // 每分钟输出一次
            console.log('💓 [WebSocketManager] 发送心跳（页面不可见）')
          }
        }

        // 设置心跳响应超时检测
        this.setHeartbeatTimeout()
      } catch (error) {
        console.error('❌ [WebSocketManager] 发送心跳失败:', error)
        this.recordConnectionEvent('heartbeat_error', { error: error.message })
        this.handleConnectionError(error)
      }
    } else {
      console.warn('⚠️ [WebSocketManager] 心跳检查时发现连接已断开')
      this.recordConnectionEvent('heartbeat_connection_lost', {
        isConnected: this.isConnected,
        socketExists: !!this.socket,
        socketDisconnected: this.socket?.disconnected
      })
      this.stopHeartbeat()
      // 如果连接断开，尝试重连
      if (this.isInitialized && this.isPageVisible) {
        console.log('🔄 [WebSocketManager] 页面可见时检测到连接断开，尝试重连')
        this.handleConnectionError(new Error('心跳检查时发现连接断开'))
      }
    }
  }

  // 设置心跳响应超时检测
  setHeartbeatTimeout() {
    // 清除之前的超时检测
    if (this.heartbeatTimeoutTimer) {
      clearTimeout(this.heartbeatTimeoutTimer)
    }

    // 设置新的超时检测
    this.heartbeatTimeoutTimer = setTimeout(() => {
      console.warn('⚠️ [WebSocketManager] 心跳响应超时')
      this.recordConnectionEvent('heartbeat_timeout', {
        lastHeartbeatTime: this.lastHeartbeatTime,
        timeout: this.heartbeatTimeout
      })

      // 心跳超时，认为连接异常
      if (this.isConnected) {
        this.handleConnectionError(new Error('心跳响应超时'))
      }
    }, this.heartbeatTimeout)
  }

  // 停止心跳
  stopHeartbeat() {
    if (this.heartbeatTimer) {
      clearInterval(this.heartbeatTimer)
      this.heartbeatTimer = null
      console.log('🔧 [WebSocketManager] 心跳机制已停止')
    }
  }

  // 启动保活机制
  startKeepAlive() {
    this.stopKeepAlive()
    console.log('🔧 [WebSocketManager] 启动保活机制，间隔:', this.keepAliveInterval + 'ms')

    this.keepAliveTimer = setInterval(() => {
      if (this.socket && this.isConnected && !this.socket.disconnected) {
        // 发送保活信号
        this.socket.emit('keep_alive', {
          clientId: this.clientId,
          timestamp: Date.now(),
          pageVisible: this.isPageVisible
        })
        console.log('💓 [WebSocketManager] 发送保活信号')
      }
    }, this.keepAliveInterval)
  }

  // 停止保活机制
  stopKeepAlive() {
    if (this.keepAliveTimer) {
      clearInterval(this.keepAliveTimer)
      this.keepAliveTimer = null
      console.log('🔧 [WebSocketManager] 保活机制已停止')
    }
  }

  // 启动连接状态检查
  startConnectionCheck() {
    this.stopConnectionCheck()
    console.log('🔧 [WebSocketManager] 启动连接状态检查，间隔:', this.connectionCheckInterval + 'ms')

    this.connectionCheckTimer = setInterval(() => {
      this.checkConnectionHealth()
    }, this.connectionCheckInterval)
  }

  // 停止连接状态检查
  stopConnectionCheck() {
    if (this.connectionCheckTimer) {
      clearInterval(this.connectionCheckTimer)
      this.connectionCheckTimer = null
      console.log('🔧 [WebSocketManager] 连接状态检查已停止')
    }
  }

  // 启动强制重连检查
  startForceReconnectCheck() {
    this.stopForceReconnectCheck()
    console.log('🔧 [WebSocketManager] 启动强制重连检查，间隔:', this.forceReconnectInterval + 'ms')

    this.forceReconnectTimer = setInterval(() => {
      this.forceConnectionCheck()
    }, this.forceReconnectInterval)
  }

  // 停止强制重连检查
  stopForceReconnectCheck() {
    if (this.forceReconnectTimer) {
      clearInterval(this.forceReconnectTimer)
      this.forceReconnectTimer = null
      console.log('🔧 [WebSocketManager] 强制重连检查已停止')
    }
  }

  // 强制连接检查
  forceConnectionCheck() {
    console.log('🔍 [WebSocketManager] 执行强制连接检查...')

    if (!this.isInitialized) {
      console.log('⚠️ [WebSocketManager] 未初始化，跳过强制检查')
      return
    }

    if (!this.isConnected || !this.socket || this.socket.disconnected) {
      console.warn('⚠️ [WebSocketManager] 强制检查发现连接断开，立即重连')
      this.forceReconnect()
    } else {
      // 发送一个测试消息确认连接
      try {
        this.socket.emit('force_connection_test', {
          timestamp: new Date().toISOString(),
          clientType: 'websocket_manager'
        })
        console.log('✅ [WebSocketManager] 强制连接检查通过')
      } catch (error) {
        console.error('❌ [WebSocketManager] 强制连接测试失败:', error)
        this.forceReconnect()
      }
    }
  }

  // 检查连接健康状态
  checkConnectionHealth() {
    if (!this.socket || !this.isConnected) {
      console.warn('⚠️ [WebSocketManager] 连接健康检查：连接已断开，尝试重连')
      if (this.isInitialized) {
        this.handleConnectionError(new Error('连接健康检查发现连接断开'))
      }
      return
    }

    // 检查心跳超时
    if (this.lastHeartbeatTime) {
      const timeSinceLastHeartbeat = Date.now() - this.lastHeartbeatTime
      const heartbeatTimeout = this.heartbeatTimeout + this.heartbeatInterval

      if (timeSinceLastHeartbeat > heartbeatTimeout) {
        console.error('❌ [WebSocketManager] 心跳超时，强制重连', {
          timeSinceLastHeartbeat,
          heartbeatTimeout,
          lastHeartbeat: new Date(this.lastHeartbeatTime).toISOString()
        })
        this.handleConnectionError(new Error('心跳超时'))
        return
      }
    }

    // 检查Socket连接状态
    if (this.socket.disconnected) {
      console.error('❌ [WebSocketManager] Socket已断开，但状态未更新，强制重连')
      this.handleConnectionError(new Error('Socket状态不一致'))
      return
    }

    // 主动测试连接
    try {
      this.socket.emit('connection_test', {
        timestamp: new Date().toISOString(),
        clientType: 'websocket_manager'
      })
      console.log('✅ [WebSocketManager] 连接健康检查通过，已发送连接测试')
    } catch (error) {
      console.error('❌ [WebSocketManager] 连接测试失败:', error)
      this.handleConnectionError(error)
    }
  }

  // 处理连接错误
  handleConnectionError(error) {
    console.error('🚨 [WebSocketManager] 连接错误:', error.message || error)

    // 更新连接状态
    this.isConnected = false
    this.stopHeartbeat()
    this.stopConnectionCheck()

    // 通知store连接断开
    store.dispatch('socket/disconnect')
    store.commit('socket/SET_CONNECTED', false)

    // 触发错误事件
    this.emitEvent('connection_error', { error: error.message || error })

    // 清理连接Promise
    this.connectionPromise = null

    // 如果已初始化，立即安排重连
    if (this.isInitialized) {
      console.log('🔄 [WebSocketManager] 连接错误，立即安排重连...')
      setTimeout(() => {
        this.scheduleReconnect()
      }, 500) // 500ms后开始重连
    } else {
      console.log('⚠️ [WebSocketManager] 连接错误，但未初始化，不进行重连')
    }
  }

  // 安排重连
  scheduleReconnect() {
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer)
    }

    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      console.error('❌ [WebSocketManager] 重连次数已达上限，重置重连计数器并继续尝试')
      // 不要完全停止重连，而是重置计数器
      this.reconnectAttempts = 0
      // 等待更长时间后继续重连
      setTimeout(() => {
        this.scheduleReconnect()
      }, 60000) // 1分钟后重新开始重连
      return
    }

    this.reconnectAttempts++

    // 更短的重连延迟，更积极的重连策略
    const delay = Math.min(
      this.reconnectDelay * Math.pow(1.5, this.reconnectAttempts - 1), // 使用1.5而不是2，减少延迟增长
      10000 // 最大延迟减少到10秒
    )

    console.log(`🔄 [WebSocketManager] 安排重连 (${this.reconnectAttempts}/${this.maxReconnectAttempts})，${delay}ms后重试`)

    // 触发重连开始事件
    this.emitEvent('reconnect_attempt', {
      attempt: this.reconnectAttempts,
      maxAttempts: this.maxReconnectAttempts,
      delay: delay
    })

    this.reconnectTimer = setTimeout(async () => {
      try {
        console.log(`🔄 [WebSocketManager] 开始第${this.reconnectAttempts}次重连尝试`)

        // 清理之前的连接
        this.cleanup()

        // 重新建立连接
        this.connectionPromise = this.connect()
        await this.connectionPromise

        console.log('✅ [WebSocketManager] 重连成功，重置重连计数器')
        this.reconnectAttempts = 0 // 重连成功后重置计数器

        this.emitEvent('reconnect_success', {
          attempt: this.reconnectAttempts
        })
      } catch (error) {
        console.error(`❌ [WebSocketManager] 第${this.reconnectAttempts}次重连失败:`, error)
        this.emitEvent('reconnect_failed', {
          attempt: this.reconnectAttempts,
          error: error.message
        })
        // 继续下一次重连尝试
        setTimeout(() => {
          this.scheduleReconnect()
        }, 2000) // 2秒后继续重连
      }
    }, delay)
  }

  // 清理连接资源
  cleanup() {
    console.log('🔧 [WebSocketManager] 清理连接资源')
    this.stopHeartbeat()
    this.stopConnectionCheck()
    this.stopForceReconnectCheck()
    this.stopKeepAlive() // 停止保活机制

    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer)
      this.reconnectTimer = null
    }

    if (this.socket) {
      // 如果不是页面关闭，发送断开通知
      if (!this.isPageClosing) {
        try {
          this.socket.emit('client_disconnect', {
            clientId: this.clientId,
            reason: 'cleanup',
            timestamp: Date.now()
          })
        } catch (error) {
          console.error('❌ [WebSocketManager] 发送断开通知失败:', error)
        }
      }
      this.socket.removeAllListeners()
      this.socket.disconnect()
      this.socket = null
    }

    this.isConnected = false
    this.lastHeartbeatTime = null
  }

  // 清理所有事件监听器
  cleanupEventListeners() {
    if (typeof document !== 'undefined' && this.visibilityChangeHandler) {
      document.removeEventListener('visibilitychange', this.visibilityChangeHandler)
      this.visibilityChangeHandler = null
    }

    if (typeof window !== 'undefined' && this.beforeUnloadHandler) {
      window.removeEventListener('beforeunload', this.beforeUnloadHandler)
      this.beforeUnloadHandler = null
    }

    if (typeof window !== 'undefined' && this.pageHideHandler) {
      window.removeEventListener('pagehide', this.pageHideHandler)
      this.pageHideHandler = null
    }
  }

  // 断开连接
  disconnect() {
    console.log('🔌 [WebSocketManager] 主动断开WebSocket连接')
    this.isInitialized = false
    this.connectionPromise = null
    this.cleanup()
    this.cleanupEventListeners()

    // 通知store连接断开
    store.dispatch('socket/disconnect')
    store.commit('socket/SET_CONNECTED', false)
  }

  // 软断开 - 用于组件切换时，不完全断开连接
  softDisconnect() {
    console.log('🔌 [WebSocketManager] 软断开 - 保持连接但暂停活动')
    // 不设置 isInitialized = false，保持初始化状态
    // 不清理事件监听器
    // 只暂停心跳和检查
    this.stopHeartbeat()
    this.stopConnectionCheck()
    this.stopForceReconnectCheck()

    console.log('🔌 [WebSocketManager] 软断开完成，连接将在需要时自动恢复')
  }

  // 恢复连接 - 用于组件重新激活时
  resumeConnection() {
    console.log('🔌 [WebSocketManager] 恢复连接活动')

    if (this.isInitialized && this.isConnected && this.socket && !this.socket.disconnected) {
      console.log('🔌 [WebSocketManager] 连接正常，恢复心跳和检查')
      this.startHeartbeat()
      this.startConnectionCheck()
      this.startForceReconnectCheck()
    } else if (this.isInitialized) {
      console.log('🔌 [WebSocketManager] 连接异常，重新建立连接')
      this.init()
    }
  }

  // 发送消息
  emit(event, data) {
    if (this.socket && this.isConnected) {
      this.socket.emit(event, data)
      return true
    } else {
      console.warn('WebSocket未连接，无法发送消息:', event)
      return false
    }
  }

  // 注册事件监听器
  on(event, handler) {
    if (!this.eventHandlers.has(event)) {
      this.eventHandlers.set(event, new Set())
    }
    this.eventHandlers.get(event).add(handler)
  }

  // 移除事件监听器
  off(event, handler) {
    if (this.eventHandlers.has(event)) {
      if (handler) {
        this.eventHandlers.get(event).delete(handler)
      } else {
        this.eventHandlers.get(event).clear()
      }
    }
  }

  // 触发事件
  emitEvent(event, data) {
    if (this.eventHandlers.has(event)) {
      this.eventHandlers.get(event).forEach(handler => {
        try {
          handler(data)
        } catch (error) {
          console.error(`事件处理器执行失败 [${event}]:`, error)
        }
      })
    }
  }

  // 获取连接状态
  getConnectionStatus() {
    return {
      isConnected: this.isConnected,
      isInitialized: this.isInitialized,
      reconnectAttempts: this.reconnectAttempts,
      type: 'websocket'
    }
  }

  // 强制重连
  forceReconnect() {
    console.log('强制重连WebSocket')
    this.reconnectAttempts = 0
    this.connectionPromise = null
    this.cleanup()
    return this.init()
  }

  // 注册Web客户端
  registerWebClient() {
    if (!this.isConnected || !this.socket) return

    const user = store.getters['auth/user']
    const userId = user ? user.id : 'anonymous'

    this.socket.emit('web_client_connect', {
      userId: userId,
      username: user ? user.username : 'anonymous',
      clientType: 'websocket_manager',
      clientId: this.clientId, // 添加客户端唯一标识
      sessionId: this.sessionId, // 会话ID
      page: 'global',
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent,
      url: window.location.href
    })

    console.log('📡 [WebSocketManager] 已注册为用户', userId, '的全局客户端, ClientID:', this.clientId)
  }

  // 请求设备状态同步
  requestDeviceStatusSync() {
    console.log('🔄 [WebSocketManager] 请求设备状态同步...')
    if (this.isConnected && this.socket) {
      this.socket.emit('request_device_status_sync')
    }
  }

  // 获取连接状态
  getConnectionStatus() {
    return {
      isConnected: this.isConnected,
      socket: this.socket,
      type: this.isConnected ? 'websocket' : 'disconnected'
    }
  }

  // 强制重连
  forceReconnect() {
    console.log('🔄 [WebSocketManager] 强制重连...')

    // 清理现有连接
    this.cleanup()

    // 重置重连计数器
    this.reconnectAttempts = 0

    // 立即开始重连
    setTimeout(() => {
      this.init()
    }, 1000)
  }

  // 检查连接状态并在需要时重连
  ensureConnected() {
    if (!this.isConnected || !this.socket || this.socket.disconnected) {
      console.log('🔄 [WebSocketManager] 检测到连接断开，开始重连...')
      this.forceReconnect()
      return false
    }
    return true
  }

  // 发送消息
  send(event, data) {
    if (this.isConnected && this.socket && !this.socket.disconnected) {
      try {
        // 记录ping时间用于延迟计算
        if (event === 'ping') {
          this.lastPingTime = Date.now()
        }

        this.socket.emit(event, data)
        return true
      } catch (error) {
        console.error('❌ [WebSocketManager] 发送消息失败:', error)
        this.recordConnectionEvent('send_error', { event, error: error.message })
        this.handleConnectionError(error)
        return false
      }
    } else {
      console.warn('⚠️ [WebSocketManager] WebSocket未连接，无法发送消息:', event, data)
      // 尝试重连
      this.ensureConnected()
      return false
    }
  }

  // 记录连接事件
  recordConnectionEvent(type, data = {}) {
    const event = {
      type,
      timestamp: Date.now(),
      data,
      clientId: this.clientId,
      isConnected: this.isConnected,
      reconnectAttempts: this.reconnectAttempts,
      pageVisible: this.isPageVisible
    }

    this.connectionEvents.push(event)

    // 限制事件记录数量
    if (this.connectionEvents.length > this.maxConnectionEvents) {
      this.connectionEvents.shift()
    }

    // 输出重要事件到控制台
    if (['connection_established', 'connection_lost', 'force_disconnect', 'heartbeat_timeout'].includes(type)) {
      console.log(`📊 [WebSocketManager] 连接事件: ${type}`, data)
    }
  }

  // 获取连接统计信息
  getConnectionStats() {
    const now = Date.now()
    const connectionDuration = this.connectionStartTime ? now - this.connectionStartTime : 0

    return {
      isConnected: this.isConnected,
      clientId: this.clientId,
      sessionId: this.sessionId,
      connectionDuration,
      reconnectAttempts: this.reconnectAttempts,
      networkLatency: this.networkLatency,
      lastHeartbeatTime: this.lastHeartbeatTime,
      lastPingTime: this.lastPingTime,
      lastPongTime: this.lastPongTime,
      pageVisible: this.isPageVisible,
      connectionEvents: this.connectionEvents.slice(-10), // 最近10个事件
      eventCounts: this.getEventCounts()
    }
  }

  // 获取事件统计
  getEventCounts() {
    const counts = {}
    this.connectionEvents.forEach(event => {
      counts[event.type] = (counts[event.type] || 0) + 1
    })
    return counts
  }

  // 计算重连延迟
  calculateReconnectDelay(reason, isPageVisible) {
    let baseDelay = 1000 // 基础延迟1秒

    // 根据断开原因调整延迟
    switch (reason) {
      case 'io client disconnect':
      case 'client namespace disconnect':
        if (isPageVisible) {
          baseDelay = 500 // 客户端断开但页面可见，可能是组件重载，快速重连
          console.log('🔄 [WebSocketManager] 检测到组件重载，快速重连')
        } else {
          baseDelay = 5000 // 页面不可见，延迟重连
          console.log('🔄 [WebSocketManager] 页面不可见，延迟重连')
        }
        break
      case 'transport close':
        baseDelay = 2000 // 传输关闭，中等延迟
        break
      case 'transport error':
        baseDelay = 3000 // 传输错误，较长延迟
        break
      case 'ping timeout':
        baseDelay = 1500 // ping超时，快速重连
        break
      default:
        baseDelay = 1000
    }

    // 根据重连次数应用指数退避
    const exponentialDelay = Math.min(
      baseDelay * Math.pow(1.5, this.reconnectAttempts),
      30000 // 最大30秒
    )

    console.log(`🔄 [WebSocketManager] 计算重连延迟: ${exponentialDelay}ms (原因: ${reason}, 尝试次数: ${this.reconnectAttempts})`)
    return exponentialDelay
  }

  // 显示连接通知
  showConnectionNotification(type, title, message) {
    // 触发事件供UI组件监听
    this.emitEvent('connection_notification', {
      type, // success, warning, error, info
      title,
      message,
      timestamp: Date.now()
    })

    // 也可以在这里集成具体的通知库，如 Element UI 的 Notification
    console.log(`🔔 [WebSocketManager] ${type.toUpperCase()}: ${title} - ${message}`)
  }

  // 显示断开连接通知
  showDisconnectionNotification(reason) {
    let title = '连接已断开'
    let message = '与服务器的连接已断开'
    let type = 'warning'

    switch (reason) {
      case 'io server disconnect':
        title = '服务器断开连接'
        message = '服务器主动断开了连接'
        type = 'error'
        break
      case 'transport close':
        title = '网络连接中断'
        message = '网络连接已中断，正在尝试重连...'
        type = 'warning'
        break
      case 'transport error':
        title = '网络错误'
        message = '网络传输出现错误，正在尝试重连...'
        type = 'error'
        break
      case 'ping timeout':
        title = '连接超时'
        message = '服务器响应超时，正在尝试重连...'
        type = 'warning'
        break
      case 'io client disconnect':
        // 客户端主动断开，通常不需要显示通知
        return
      default:
        message = `连接断开 (${reason})，正在尝试重连...`
    }

    this.showConnectionNotification(type, title, message)
  }

  // 清理连接事件记录
  clearConnectionEvents() {
    this.connectionEvents = []
    console.log('🧹 [WebSocketManager] 已清理连接事件记录')
  }
}

// 创建全局实例
const wsManager = new WebSocketManager()

// 导出函数
export function initWebSocket() {
  return wsManager.init()
}

export function getWebSocketManager() {
  return wsManager
}

export function disconnectWebSocket() {
  wsManager.disconnect()
}

// 确保连接状态
export async function ensureConnection() {
  if (!wsManager.isConnected) {
    console.log('🔧 [WebSocketManager] 连接未建立，尝试初始化...')
    await wsManager.init()
  }
  return wsManager.isConnected
}

// 兼容原有接口
export function initSocket() {
  return wsManager.init()
}

export function disconnectSocket() {
  wsManager.disconnect()
}

export default wsManager
