<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WebSocket长效连接测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            font-weight: bold;
        }
        .connected { background-color: #d4edda; color: #155724; }
        .disconnected { background-color: #f8d7da; color: #721c24; }
        .reconnecting { background-color: #fff3cd; color: #856404; }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background-color: #0056b3; }
        button:disabled { background-color: #6c757d; cursor: not-allowed; }
        .log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .info-card {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 6px;
            border-left: 4px solid #007bff;
        }
        .info-card h4 {
            margin: 0 0 10px 0;
            color: #495057;
        }
        .info-card p {
            margin: 5px 0;
            color: #6c757d;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>WebSocket长效连接测试</h1>
        <p>此页面用于测试WebSocket长效连接、心跳机制、自动重连和单一客户端控制功能。</p>
        
        <div id="connectionStatus" class="status disconnected">
            连接状态: 未连接
        </div>

        <div class="info-grid">
            <div class="info-card">
                <h4>连接信息</h4>
                <p>客户端ID: <span id="clientId">-</span></p>
                <p>会话ID: <span id="sessionId">-</span></p>
                <p>用户ID: <span id="userId">-</span></p>
                <p>连接时间: <span id="connectTime">-</span></p>
            </div>
            <div class="info-card">
                <h4>统计信息</h4>
                <p>重连次数: <span id="reconnectCount">0</span></p>
                <p>心跳次数: <span id="heartbeatCount">0</span></p>
                <p>保活次数: <span id="keepAliveCount">0</span></p>
                <p>最后心跳: <span id="lastHeartbeat">-</span></p>
            </div>
            <div class="info-card">
                <h4>页面状态</h4>
                <p>页面可见: <span id="pageVisible">是</span></p>
                <p>连接时长: <span id="connectionDuration">-</span></p>
                <p>服务器地址: <span id="serverUrl">-</span></p>
            </div>
        </div>

        <div>
            <button id="connectBtn" onclick="connect()">连接</button>
            <button id="disconnectBtn" onclick="disconnect()" disabled>断开</button>
            <button onclick="sendHeartbeat()">发送心跳</button>
            <button onclick="sendKeepAlive()">发送保活</button>
            <button onclick="clearLog()">清空日志</button>
            <button onclick="testDuplicateLogin()">测试重复登录</button>
        </div>
    </div>

    <div class="container">
        <h3>连接日志</h3>
        <div id="log" class="log"></div>
    </div>

    <script src="/socket.io/socket.io.js"></script>
    <script>
        let socket = null;
        let isConnected = false;
        let reconnectCount = 0;
        let heartbeatCount = 0;
        let keepAliveCount = 0;
        let connectTime = null;
        let clientId = null;
        let sessionId = null;
        let durationTimer = null;

        // 生成客户端ID
        function generateClientId() {
            const timestamp = Date.now();
            const random = Math.random().toString(36).substr(2, 9);
            return `test_${timestamp}_${random}`;
        }

        // 记录日志
        function log(message) {
            const logElement = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            logElement.innerHTML += `[${timestamp}] ${message}\n`;
            logElement.scrollTop = logElement.scrollHeight;
            console.log(`[${timestamp}] ${message}`);
        }

        // 更新UI状态
        function updateStatus(status, className) {
            const statusElement = document.getElementById('connectionStatus');
            statusElement.textContent = `连接状态: ${status}`;
            statusElement.className = `status ${className}`;
        }

        // 更新连接信息
        function updateConnectionInfo() {
            document.getElementById('clientId').textContent = clientId || '-';
            document.getElementById('sessionId').textContent = sessionId || '-';
            document.getElementById('userId').textContent = 'test_user';
            document.getElementById('connectTime').textContent = connectTime ? connectTime.toLocaleTimeString() : '-';
            document.getElementById('reconnectCount').textContent = reconnectCount;
            document.getElementById('heartbeatCount').textContent = heartbeatCount;
            document.getElementById('keepAliveCount').textContent = keepAliveCount;
            document.getElementById('pageVisible').textContent = document.hidden ? '否' : '是';
            document.getElementById('serverUrl').textContent = window.location.origin;
        }

        // 更新连接时长
        function updateDuration() {
            if (connectTime && isConnected) {
                const duration = Math.floor((Date.now() - connectTime.getTime()) / 1000);
                const minutes = Math.floor(duration / 60);
                const seconds = duration % 60;
                document.getElementById('connectionDuration').textContent = `${minutes}分${seconds}秒`;
            } else {
                document.getElementById('connectionDuration').textContent = '-';
            }
        }

        // 连接WebSocket
        function connect() {
            if (socket && isConnected) {
                log('已经连接，无需重复连接');
                return;
            }

            clientId = generateClientId();
            log(`开始连接WebSocket，客户端ID: ${clientId}`);
            updateStatus('连接中...', 'reconnecting');

            socket = io({
                transports: ['websocket'],
                timeout: 30000,
                reconnection: false,
                forceNew: true,
                upgrade: false,
                autoConnect: true,
                pingTimeout: 120000,
                pingInterval: 30000,
                query: {
                    clientId: clientId,
                    clientType: 'test_client'
                }
            });

            // 连接成功
            socket.on('connect', () => {
                isConnected = true;
                connectTime = new Date();
                updateStatus('已连接', 'connected');
                log('WebSocket连接成功');
                
                // 注册为Web客户端
                socket.emit('web_client_connect', {
                    userId: 'test_user',
                    username: 'test_user',
                    clientType: 'test_client',
                    clientId: clientId,
                    timestamp: new Date().toISOString(),
                    userAgent: navigator.userAgent,
                    url: window.location.href
                });

                document.getElementById('connectBtn').disabled = true;
                document.getElementById('disconnectBtn').disabled = false;
                
                // 启动时长计时器
                durationTimer = setInterval(updateDuration, 1000);
                updateConnectionInfo();
            });

            // 连接确认
            socket.on('connection_confirmed', (data) => {
                sessionId = data.sessionId;
                log(`收到连接确认，会话ID: ${sessionId}`);
                updateConnectionInfo();
            });

            // 强制断开
            socket.on('force_disconnect', (data) => {
                log(`收到强制断开通知: ${data.message} (原因: ${data.reason})`);
                if (data.reason === 'duplicate_login') {
                    alert('检测到重复登录，当前连接将被断开！');
                }
            });

            // 断开连接
            socket.on('disconnect', (reason) => {
                isConnected = false;
                updateStatus(`已断开 (${reason})`, 'disconnected');
                log(`WebSocket连接断开，原因: ${reason}`);
                
                document.getElementById('connectBtn').disabled = false;
                document.getElementById('disconnectBtn').disabled = true;
                
                if (durationTimer) {
                    clearInterval(durationTimer);
                    durationTimer = null;
                }
                updateConnectionInfo();
                
                // 如果不是主动断开，尝试重连
                if (reason !== 'io client disconnect' && reason !== 'transport close') {
                    reconnectCount++;
                    log(`准备重连 (第${reconnectCount}次)`);
                    setTimeout(() => {
                        if (!isConnected) {
                            connect();
                        }
                    }, 3000);
                }
            });

            // 心跳响应
            socket.on('pong', () => {
                heartbeatCount++;
                document.getElementById('lastHeartbeat').textContent = new Date().toLocaleTimeString();
                updateConnectionInfo();
            });

            // 其他事件
            socket.on('test_event', (data) => {
                log(`收到测试事件: ${JSON.stringify(data)}`);
            });
        }

        // 断开连接
        function disconnect() {
            if (socket && isConnected) {
                log('主动断开WebSocket连接');
                socket.disconnect();
            }
        }

        // 发送心跳
        function sendHeartbeat() {
            if (socket && isConnected) {
                socket.emit('ping');
                log('发送心跳信号');
            } else {
                log('未连接，无法发送心跳');
            }
        }

        // 发送保活
        function sendKeepAlive() {
            if (socket && isConnected) {
                keepAliveCount++;
                socket.emit('keep_alive', {
                    clientId: clientId,
                    timestamp: Date.now(),
                    pageVisible: !document.hidden
                });
                log('发送保活信号');
                updateConnectionInfo();
            } else {
                log('未连接，无法发送保活信号');
            }
        }

        // 清空日志
        function clearLog() {
            document.getElementById('log').innerHTML = '';
        }

        // 测试重复登录
        function testDuplicateLogin() {
            // 在新窗口中打开相同页面来测试重复登录
            window.open(window.location.href, '_blank');
            log('已打开新窗口测试重复登录功能');
        }

        // 页面可见性变化监听
        document.addEventListener('visibilitychange', () => {
            const isVisible = !document.hidden;
            log(`页面可见性变化: ${isVisible ? '可见' : '隐藏'}`);
            updateConnectionInfo();
        });

        // 页面加载完成后自动连接
        window.addEventListener('load', () => {
            log('页面加载完成');
            updateConnectionInfo();
            // 自动连接
            setTimeout(connect, 1000);
        });

        // 页面关闭前处理
        window.addEventListener('beforeunload', () => {
            if (socket && isConnected) {
                socket.emit('client_closing', {
                    clientId: clientId,
                    reason: 'page_unload'
                });
            }
        });
    </script>
</body>
</html>
